{"version": 3, "file": "index.umd.js", "sources": ["../src/table-cell.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableCellOptions {\n  /**\n   * The HTML attributes for a table cell node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table cells.\n * @see https://www.tiptap.dev/api/nodes/table-cell\n */\nexport const TableCell = Node.create<TableCellOptions>({\n  name: 'tableCell',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth\n            ? colwidth.split(',').map(width => parseInt(width, 10))\n            : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [\n      { tag: 'td' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['td', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;EAWA;;;EAGG;AACU,QAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;EACrD,IAAA,IAAI,EAAE,WAAW;MAEjB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,OAAO,EAAE,QAAQ;MAEjB,aAAa,GAAA;UACX,OAAO;EACL,YAAA,OAAO,EAAE;EACP,gBAAA,OAAO,EAAE,CAAC;EACX,aAAA;EACD,YAAA,OAAO,EAAE;EACP,gBAAA,OAAO,EAAE,CAAC;EACX,aAAA;EACD,YAAA,QAAQ,EAAE;EACR,gBAAA,OAAO,EAAE,IAAI;kBACb,SAAS,EAAE,OAAO,IAAG;sBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;sBACjD,MAAM,KAAK,GAAG;4BACV,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;4BACpD,IAAI;EAER,oBAAA,OAAO,KAAK;mBACb;EACF,aAAA;WACF;OACF;EAED,IAAA,SAAS,EAAE,MAAM;EAEjB,IAAA,SAAS,EAAE,IAAI;MAEf,SAAS,GAAA;UACP,OAAO;cACL,EAAE,GAAG,EAAE,IAAI,EAAE;WACd;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC/E;EAEF,CAAA;;;;;;;;;;;"}