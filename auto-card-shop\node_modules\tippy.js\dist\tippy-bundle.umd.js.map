{"version": 3, "file": "tippy-bundle.umd.js", "sources": ["../src/css.ts", "../src/browser.ts", "../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/validation.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/createSingleton.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/bundle-umd.js"], "sourcesContent": ["export function injectCSS(css: string): void {\n  const style = document.createElement('style');\n  style.textContent = css;\n  style.setAttribute('data-__NAMESPACE_PREFIX__-stylesheet', '');\n  const head = document.head;\n  const firstStyleOrLinkTag = document.querySelector('head>style,head>link');\n\n  if (firstStyleOrLinkTag) {\n    head.insertBefore(style, firstStyleOrLinkTag);\n  } else {\n    head.appendChild(style);\n  }\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import css from '../dist/tippy.css';\nimport {injectCSS} from '../src/css';\nimport {isBrowser} from '../src/browser';\nimport tippy, {hideAll} from '../src';\nimport createSingleton from '../src/addons/createSingleton';\nimport delegate from '../src/addons/delegate';\nimport animateFill from '../src/plugins/animateFill';\nimport followCursor from '../src/plugins/followCursor';\nimport inlinePositioning from '../src/plugins/inlinePositioning';\nimport sticky from '../src/plugins/sticky';\nimport {ROUND_ARROW} from '../src/constants';\nimport {render} from '../src/template';\n\nif (isBrowser) {\n  injectCSS(css);\n}\n\ntippy.setDefaultProps({\n  plugins: [animateFill, followCursor, inlinePositioning, sticky],\n  render,\n});\n\ntippy.createSingleton = createSingleton;\ntippy.delegate = delegate;\ntippy.hideAll = hideAll;\ntippy.roundArrow = ROUND_ARROW;\n\nexport default tippy;\n"], "names": ["injectCSS", "css", "style", "document", "createElement", "textContent", "setAttribute", "head", "firstStyleOrLinkTag", "querySelector", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "window", "isIE11", "msCrypto", "ROUND_ARROW", "BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "transitionDuration", "setVisibilityState", "state", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "applyStylesModifier", "applyStyles", "effect", "initialStyles", "position", "strategy", "margin", "assign", "styles", "createSingleton", "tippyInstances", "individualInstances", "references", "triggerTargets", "overrides", "interceptSetPropsCleanups", "shownOnCreate", "setTriggerTargets", "setReferences", "enableInstances", "interceptSetProps", "singleton", "originalSetProps", "prepareInstance", "overrideProps", "originalShow", "ref", "showNext", "first", "showPrevious", "last", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "disabled", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "originalEnable", "originalDisable", "shouldDestroyChildInstances", "createBackdropElement", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "triedPlacements", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB", "roundArrow"], "mappings": ";;;;;;;;;;;;;EAAO,SAASA,SAAT,CAAmBC,GAAnB,EAAsC;EAC3C,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAd;EACAF,EAAAA,KAAK,CAACG,WAAN,GAAoBJ,GAApB;EACAC,EAAAA,KAAK,CAACI,YAAN,CAAmB,uBAAnB,EAA2D,EAA3D;EACA,MAAMC,IAAI,GAAGJ,QAAQ,CAACI,IAAtB;EACA,MAAMC,mBAAmB,GAAGL,QAAQ,CAACM,aAAT,CAAuB,sBAAvB,CAA5B;;EAEA,MAAID,mBAAJ,EAAyB;EACvBD,IAAAA,IAAI,CAACG,YAAL,CAAkBR,KAAlB,EAAyBM,mBAAzB;EACD,GAFD,MAEO;EACLD,IAAAA,IAAI,CAACI,WAAL,CAAiBT,KAAjB;EACD;EACF;;ECZM,IAAMU,SAAS,GACpB,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOV,QAAP,KAAoB,WADhD;AAGP,EAAO,IAAMW,MAAM,GAAGF,SAAS;EAE3B,CAAC,CAACC,MAAM,CAACE,QAFkB,GAG3B,KAHG;;ECHA,IAAMC,WAAW,GACtB,0LADK;AAGP,EAAO,IAAMC,SAAS,cAAf;AACP,EAAO,IAAMC,aAAa,kBAAnB;AACP,EAAO,IAAMC,cAAc,mBAApB;AACP,EAAO,IAAMC,WAAW,gBAAjB;AACP,EAAO,IAAMC,eAAe,oBAArB;AAEP,EAAO,IAAMC,aAAa,GAAG;EAACC,EAAAA,OAAO,EAAE,IAAV;EAAgBC,EAAAA,OAAO,EAAE;EAAzB,CAAtB;AAEP,EAAO,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B;EAAA,SAAMtB,QAAQ,CAACuB,IAAf;EAAA,CAAhC;;ECTA,SAASC,cAAT,CACLC,GADK,EAELC,GAFK,EAGI;EACT,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;EACD;AAED,EAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;EACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;EACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;EAKD;;EAED,SAAOL,KAAP;EACD;AAED,EAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;EACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;EACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;EACD;AAED,EAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;EACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;EACD;AAED,EAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;EAClB;EACA,MAAIA,EAAE,KAAK,CAAX,EAAc;EACZ,WAAOD,EAAP;EACD;;EAED,MAAIE,OAAJ;EAEA,SAAO,UAACC,GAAD,EAAe;EACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;EACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;EACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;EACD,KAFmB,EAEjBF,EAFiB,CAApB;EAGD,GALD;EAMD;AAED,EAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;EACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;EACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;EACD,GAFD;EAGA,SAAOyB,KAAP;EACD;AAED,EAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;EACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;EACD;AAED,EAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;EACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;EACD;AAED,EAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;EACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;EAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;EACD;EACF;AAED,EAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;EACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;EAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;EAAA,GAAX,CAAP;EACD;AAED,EAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;EACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;EACD;AAED,EAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;EACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;EACD;AAED,EAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;EAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;EAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;EACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;EACD;;EAED,WAAO6C,GAAP;EACD,GANM,EAMJ,EANI,CAAP;EAOD;;ECtGM,SAASE,GAAT,GAA+B;EACpC,SAAOzE,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAP;EACD;AAED,EAAO,SAASyE,SAAT,CAAmB7C,KAAnB,EAAwE;EAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwB8C,IAAxB,CAA6B,UAACvC,IAAD;EAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;EAAA,GAA7B,CAAP;EACD;AAED,EAAO,SAASwC,UAAT,CAAoB/C,KAApB,EAAuD;EAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;EACD;AAED,EAAO,SAASgD,YAAT,CAAsBhD,KAAtB,EAA2D;EAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;EACD;AAED,EAAO,SAASiD,kBAAT,CAA4BjD,KAA5B,EAAmE;EACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACkD,MAAf,IAAyBlD,KAAK,CAACkD,MAAN,CAAaC,SAAb,KAA2BnD,KAAtD,CAAR;EACD;AAED,EAAO,SAASoD,kBAAT,CAA4BpD,KAA5B,EAAuD;EAC5D,MAAI6C,SAAS,CAAC7C,KAAD,CAAb,EAAsB;EACpB,WAAO,CAACA,KAAD,CAAP;EACD;;EAED,MAAI+C,UAAU,CAAC/C,KAAD,CAAd,EAAuB;EACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;EACD;;EAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,WAAOA,KAAP;EACD;;EAED,SAAOqC,SAAS,CAAClE,QAAQ,CAACkF,gBAAT,CAA0BrD,KAA1B,CAAD,CAAhB;EACD;AAED,EAAO,SAASsD,qBAAT,CACLC,GADK,EAELvD,KAFK,EAGC;EACNuD,EAAAA,GAAG,CAAChC,OAAJ,CAAY,UAACiC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAACtF,KAAH,CAASuF,kBAAT,GAAiCzD,KAAjC;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAAS0D,kBAAT,CACLH,GADK,EAELI,KAFK,EAGC;EACNJ,EAAAA,GAAG,CAAChC,OAAJ,CAAY,UAACiC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAAClF,YAAH,CAAgB,YAAhB,EAA8BqF,KAA9B;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAASC,gBAAT,CACLC,iBADK,EAEK;EAAA;;EACV,0BAAkBjC,gBAAgB,CAACiC,iBAAD,CAAlC;EAAA,MAAOC,OAAP,wBADU;;;EAIV,SAAOA,OAAO,QAAP,6BAAAA,OAAO,CAAEC,aAAT,mCAAwBrE,IAAxB,GAA+BoE,OAAO,CAACC,aAAvC,GAAuD5F,QAA9D;EACD;AAED,EAAO,SAAS6F,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;EACT,MAAOC,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;EAAA,MAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;EAEA,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;EAAA,QAApCC,UAAoC,QAApCA,UAAoC;EAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;EAAA,QAAXC,KAAW,QAAXA,KAAW;EAChE,QAAOC,iBAAP,GAA4BD,KAA5B,CAAOC,iBAAP;EACA,QAAMC,aAAa,GAAGvC,gBAAgB,CAACoC,WAAW,CAACnC,SAAb,CAAtC;EACA,QAAMuC,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;EAEA,QAAI,CAACF,UAAL,EAAiB;EACf,aAAO,IAAP;EACD;;EAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;EACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;EACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;EACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;EAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;EAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;EAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;EAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;EAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;EACD,GAxBM,CAAP;EAyBD;AAED,EAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;EACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;EAMN;;EACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyCvE,OAAzC,CAAiD,UAAC2C,KAAD,EAAW;EAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;EACD,GAFD;EAGD;EAED;EACA;EACA;EACA;;AACA,EAAO,SAASE,cAAT,CAAwBC,MAAxB,EAAyCC,KAAzC,EAAkE;EACvE,MAAIC,MAAM,GAAGD,KAAb;;EACA,SAAOC,MAAP,EAAe;EAAA;;EACb,QAAIF,MAAM,CAACG,QAAP,CAAgBD,MAAhB,CAAJ,EAA6B;EAC3B,aAAO,IAAP;EACD;;EACDA,IAAAA,MAAM,GAAIA,MAAM,CAACE,WAAX,2CAAIF,MAAM,CAACE,WAAP,EAAJ,qBAAG,oBAAiCC,IAA1C;EACD;;EACD,SAAO,KAAP;EACD;;EClIM,IAAMC,YAAY,GAAG;EAACC,EAAAA,OAAO,EAAE;EAAV,CAArB;EACP,IAAIC,iBAAiB,GAAG,CAAxB;EAEA;EACA;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASC,oBAAT,GAAsC;EAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;EAEA,MAAI5H,MAAM,CAAC+H,WAAX,EAAwB;EACtBzI,IAAAA,QAAQ,CAAC0I,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;EACD;EACF;EAED;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASA,mBAAT,GAAqC;EAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;EAEA,MAAIA,GAAG,GAAGL,iBAAN,GAA0B,EAA9B,EAAkC;EAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;EAEAtI,IAAAA,QAAQ,CAAC6I,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;EACD;;EAEDJ,EAAAA,iBAAiB,GAAGK,GAApB;EACD;EAED;EACA;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASE,YAAT,GAA8B;EACnC,MAAMC,aAAa,GAAG/I,QAAQ,CAAC+I,aAA/B;;EAEA,MAAIjE,kBAAkB,CAACiE,aAAD,CAAtB,EAAuC;EACrC,QAAMC,QAAQ,GAAGD,aAAa,CAAChE,MAA/B;;EAEA,QAAIgE,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAACxD,KAAT,CAAe0D,SAA1C,EAAqD;EACnDH,MAAAA,aAAa,CAACE,IAAd;EACD;EACF;EACF;AAED,EAAe,SAASE,wBAAT,GAA0C;EACvDnJ,EAAAA,QAAQ,CAAC0I,gBAAT,CAA0B,YAA1B,EAAwCF,oBAAxC,EAA8DrH,aAA9D;EACAT,EAAAA,MAAM,CAACgI,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;EACD;;EC5DM,SAASM,uBAAT,CAAiCvB,MAAjC,EAAyD;EAC9D,MAAMwB,GAAG,GAAGxB,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;EAEA,SAAO,CACFA,MADE,0BACyBwB,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;EAID;AAED,EAAO,SAASC,KAAT,CAAe1H,KAAf,EAAsC;EAC3C,MAAM2H,aAAa,GAAG,YAAtB;EACA,MAAMC,mBAAmB,GAAG,WAA5B;EAEA,SAAO5H,KAAK,CACT6H,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;EAID;;EAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;EAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;EAOD;;AAED,EAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;EAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;EAGL,wDAHK;EAKL,oBALK;EAOL,mBAPK,CAAP;EASD;;EAGD,IAAIE,eAAJ;;AACA,EAAa;EACXC,EAAAA,oBAAoB;EACrB;;AAED,EAAO,SAASA,oBAAT,GAAsC;EAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;EACD;AAED,EAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;EAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;EACD;EACF;AAED,EAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;EACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;EACD;EACF;AAED,EAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;EACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;EACA,MAAME,kBAAkB,GACtBxG,MAAM,CAACyG,SAAP,CAAiBxI,QAAjB,CAA0BX,IAA1B,CAA+BgJ,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBjC,gBAFpB;EAIA8B,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;EAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;ECjFD,IAAM0B,WAAW,GAAG;EAClBC,EAAAA,WAAW,EAAE,KADK;EAElBC,EAAAA,YAAY,EAAE,KAFI;EAGlBC,EAAAA,iBAAiB,EAAE,KAHD;EAIlBC,EAAAA,MAAM,EAAE;EAJU,CAApB;EAOA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,KADO;EAElBC,EAAAA,SAAS,EAAE,MAFO;EAGlBC,EAAAA,KAAK,EAAE,IAHW;EAIlBC,EAAAA,OAAO,EAAE,EAJS;EAKlBC,EAAAA,OAAO,EAAE,KALS;EAMlBC,EAAAA,QAAQ,EAAE,GANQ;EAOlBC,EAAAA,IAAI,EAAE,SAPY;EAQlBC,EAAAA,KAAK,EAAE,EARW;EASlBC,EAAAA,MAAM,EAAE;EATU,CAApB;AAYA,EAAO,IAAMC,YAA0B;EACrCC,EAAAA,QAAQ,EAAE1K,uBAD2B;EAErC2K,EAAAA,IAAI,EAAE;EACJR,IAAAA,OAAO,EAAE,MADL;EAEJS,IAAAA,QAAQ,EAAE;EAFN,GAF+B;EAMrCC,EAAAA,KAAK,EAAE,CAN8B;EAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;EAQrCC,EAAAA,sBAAsB,EAAE,IARa;EASrCC,EAAAA,WAAW,EAAE,IATwB;EAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;EAWrCC,EAAAA,WAAW,EAAE,KAXwB;EAYrClG,EAAAA,iBAAiB,EAAE,CAZkB;EAarCmG,EAAAA,mBAAmB,EAAE,CAbgB;EAcrCC,EAAAA,cAAc,EAAE,EAdqB;EAerChG,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;EAgBrCiG,EAAAA,aAhBqC,2BAgBrB,EAhBqB;EAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;EAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;EAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;EAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;EAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;EAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;EAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;EAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;EAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;EA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;EA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;EA4BrCrJ,EAAAA,SAAS,EAAE,KA5B0B;EA6BrCsJ,EAAAA,OAAO,EAAE,EA7B4B;EA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;EA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;EAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;EAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;EAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;EAmCrCC,EAAAA,aAAa,EAAE;EAnCsB,GAoClC7C,WApCkC,EAqClCK,WArCkC,CAAhC;EAwCP,IAAMyC,WAAW,GAAGzJ,MAAM,CAACnB,IAAP,CAAY6I,YAAZ,CAApB;AAEA,EAAO,IAAMgC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;EACzE;EACA,EAAa;EACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;EACD;;EAED,MAAM9K,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAY8K,YAAZ,CAAb;EACA9K,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACnBqK,IAAAA,YAAD,CAAsBrK,GAAtB,IAA6BsM,YAAY,CAACtM,GAAD,CAAzC;EACD,GAFD;EAGD,CAVM;AAYP,EAAO,SAASwM,sBAAT,CACLC,WADK,EAEW;EAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;EACA,MAAMvC,WAAW,GAAGuC,OAAO,CAACjJ,MAAR,CAAwC,UAACC,GAAD,EAAM6J,MAAN,EAAiB;EAC3E,QAAOC,IAAP,GAA6BD,MAA7B,CAAOC,IAAP;EAAA,QAAatM,YAAb,GAA6BqM,MAA7B,CAAarM,YAAb;;EAEA,QAAIsM,IAAJ,EAAU;EAAA;;EACR9J,MAAAA,GAAG,CAAC8J,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsB7J,SAAtB,GACI2J,WAAW,CAACE,IAAD,CADf,YAEKtC,YAAD,CAAsBsC,IAAtB,CAFJ,oBAEmCtM,YAHrC;EAID;;EAED,WAAOwC,GAAP;EACD,GAXmB,EAWjB,EAXiB,CAApB;EAaA,2BACK4J,WADL,EAEKnD,WAFL;EAID;AAED,EAAO,SAASsD,qBAAT,CACLtJ,SADK,EAELuI,OAFK,EAGoB;EACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpBlJ,MAAM,CAACnB,IAAP,CAAYgL,sBAAsB,mBAAKnC,YAAL;EAAmBwB,IAAAA,OAAO,EAAPA;EAAnB,KAAlC,CADoB,GAEpBO,WAFJ;EAIA,MAAMzH,KAAK,GAAGkI,QAAQ,CAACjK,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;EACtD,QAAM8M,aAAa,GAAG,CACpBxJ,SAAS,CAACyJ,YAAV,iBAAqC/M,GAArC,KAA+C,EAD3B,EAEpBiI,IAFoB,EAAtB;;EAIA,QAAI,CAAC6E,aAAL,EAAoB;EAClB,aAAOjK,GAAP;EACD;;EAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;EACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAW8M,aAAX;EACD,KAFD,MAEO;EACL,UAAI;EACFjK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWgN,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;EACD,OAFD,CAEE,OAAOI,CAAP,EAAU;EACVrK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAW8M,aAAX;EACD;EACF;;EAED,WAAOjK,GAAP;EACD,GArBW,EAsBZ,EAtBY,CAAd;EAyBA,SAAO8B,KAAP;EACD;AAED,EAAO,SAASwI,aAAT,CACL7J,SADK,EAELqB,KAFK,EAGE;EACP,MAAMyI,GAAG,qBACJzI,KADI;EAEPoF,IAAAA,OAAO,EAAEjJ,sBAAsB,CAAC6D,KAAK,CAACoF,OAAP,EAAgB,CAACzG,SAAD,CAAhB;EAFxB,KAGHqB,KAAK,CAACkG,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAACtJ,SAAD,EAAYqB,KAAK,CAACkH,OAAlB,CALlB,CAAT;EAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKF,YAAY,CAACE,IADlB,EAEK6C,GAAG,CAAC7C,IAFT;EAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;EACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+B7F,KAAK,CAACmG,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;EAGTT,IAAAA,OAAO,EACLqD,GAAG,CAAC7C,IAAJ,CAASR,OAAT,KAAqB,MAArB,GACIpF,KAAK,CAACmG,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAASR;EARN,GAAX;EAWA,SAAOqD,GAAP;EACD;AAED,EAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;EAAA,MAFNS,YAEM;EAFNA,IAAAA,YAEM,GAFyB,EAEzB;EAAA;;EAAA,MADNT,OACM;EADNA,IAAAA,OACM,GADc,EACd;EAAA;;EACN,MAAMrK,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAY8K,YAAZ,CAAb;EACA9K,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC2L,IAAD,EAAU;EACrB,QAAMC,cAAc,GAAG/L,gBAAgB,CACrC8I,YADqC,EAErC1H,MAAM,CAACnB,IAAP,CAAY8H,WAAZ,CAFqC,CAAvC;EAKA,QAAIiE,kBAAkB,GAAG,CAACzN,cAAc,CAACwN,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;EASrB,QAAIE,kBAAJ,EAAwB;EACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAAChK,MAAR,CAAe,UAAC6K,MAAD;EAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;EAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;EAED;;EAEDhF,IAAAA,QAAQ,CACN+E,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOEzF,IAPF,CAOO,GAPP,CAFM,CAAR;EAWD,GAzBD;EA0BD;;EC9LD,IAAM6F,SAAS,GAAG,SAAZA,SAAY;EAAA,SAAmB,WAAnB;EAAA,CAAlB;;EAEA,SAASC,uBAAT,CAAiCzJ,OAAjC,EAAmD0J,IAAnD,EAAuE;EACrE1J,EAAAA,OAAO,CAACwJ,SAAS,EAAV,CAAP,GAAuBE,IAAvB;EACD;;EAED,SAASC,kBAAT,CAA4BzN,KAA5B,EAAmE;EACjE,MAAM2J,KAAK,GAAG/G,GAAG,EAAjB;;EAEA,MAAI5C,KAAK,KAAK,IAAd,EAAoB;EAClB2J,IAAAA,KAAK,CAAC+D,SAAN,GAAkBtO,WAAlB;EACD,GAFD,MAEO;EACLuK,IAAAA,KAAK,CAAC+D,SAAN,GAAkBrO,eAAlB;;EAEA,QAAIwD,SAAS,CAAC7C,KAAD,CAAb,EAAsB;EACpB2J,MAAAA,KAAK,CAAChL,WAAN,CAAkBqB,KAAlB;EACD,KAFD,MAEO;EACLuN,MAAAA,uBAAuB,CAAC5D,KAAD,EAAQ3J,KAAR,CAAvB;EACD;EACF;;EAED,SAAO2J,KAAP;EACD;;AAED,EAAO,SAASgE,UAAT,CAAoB/D,OAApB,EAA6CpF,KAA7C,EAAiE;EACtE,MAAI3B,SAAS,CAAC2B,KAAK,CAACoF,OAAP,CAAb,EAA8B;EAC5B2D,IAAAA,uBAAuB,CAAC3D,OAAD,EAAU,EAAV,CAAvB;EACAA,IAAAA,OAAO,CAACjL,WAAR,CAAoB6F,KAAK,CAACoF,OAA1B;EACD,GAHD,MAGO,IAAI,OAAOpF,KAAK,CAACoF,OAAb,KAAyB,UAA7B,EAAyC;EAC9C,QAAIpF,KAAK,CAACiF,SAAV,EAAqB;EACnB8D,MAAAA,uBAAuB,CAAC3D,OAAD,EAAUpF,KAAK,CAACoF,OAAhB,CAAvB;EACD,KAFD,MAEO;EACLA,MAAAA,OAAO,CAACvL,WAAR,GAAsBmG,KAAK,CAACoF,OAA5B;EACD;EACF;EACF;AAED,EAAO,SAASgE,WAAT,CAAqBC,MAArB,EAA4D;EACjE,MAAMhI,GAAG,GAAGgI,MAAM,CAACC,iBAAnB;EACA,MAAMC,WAAW,GAAG1L,SAAS,CAACwD,GAAG,CAACmI,QAAL,CAA7B;EAEA,SAAO;EACLnI,IAAAA,GAAG,EAAHA,GADK;EAEL+D,IAAAA,OAAO,EAAEmE,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aAAUA,IAAI,CAACC,SAAL,CAAe9H,QAAf,CAAwBnH,aAAxB,CAAV;EAAA,KAAjB,CAFJ;EAGLyK,IAAAA,KAAK,EAAEoE,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;EAAA,aACEA,IAAI,CAACC,SAAL,CAAe9H,QAAf,CAAwBjH,WAAxB,KACA8O,IAAI,CAACC,SAAL,CAAe9H,QAAf,CAAwBhH,eAAxB,CAFF;EAAA,KADK,CAHF;EAQL+O,IAAAA,QAAQ,EAAEL,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aACzBA,IAAI,CAACC,SAAL,CAAe9H,QAAf,CAAwBlH,cAAxB,CADyB;EAAA,KAAjB;EARL,GAAP;EAYD;AAED,EAAO,SAASyM,MAAT,CACLzE,QADK,EAKL;EACA,MAAM0G,MAAM,GAAGjL,GAAG,EAAlB;EAEA,MAAMiD,GAAG,GAAGjD,GAAG,EAAf;EACAiD,EAAAA,GAAG,CAAC6H,SAAJ,GAAgBzO,SAAhB;EACA4G,EAAAA,GAAG,CAACvH,YAAJ,CAAiB,YAAjB,EAA+B,QAA/B;EACAuH,EAAAA,GAAG,CAACvH,YAAJ,CAAiB,UAAjB,EAA6B,IAA7B;EAEA,MAAMsL,OAAO,GAAGhH,GAAG,EAAnB;EACAgH,EAAAA,OAAO,CAAC8D,SAAR,GAAoBxO,aAApB;EACA0K,EAAAA,OAAO,CAACtL,YAAR,CAAqB,YAArB,EAAmC,QAAnC;EAEAqP,EAAAA,UAAU,CAAC/D,OAAD,EAAUzC,QAAQ,CAAC3C,KAAnB,CAAV;EAEAqJ,EAAAA,MAAM,CAAClP,WAAP,CAAmBkH,GAAnB;EACAA,EAAAA,GAAG,CAAClH,WAAJ,CAAgBiL,OAAhB;EAEAyE,EAAAA,QAAQ,CAAClH,QAAQ,CAAC3C,KAAV,EAAiB2C,QAAQ,CAAC3C,KAA1B,CAAR;;EAEA,WAAS6J,QAAT,CAAkBC,SAAlB,EAAoCC,SAApC,EAA4D;EAC1D,uBAA8BX,WAAW,CAACC,MAAD,CAAzC;EAAA,QAAOhI,GAAP,gBAAOA,GAAP;EAAA,QAAY+D,OAAZ,gBAAYA,OAAZ;EAAA,QAAqBD,KAArB,gBAAqBA,KAArB;;EAEA,QAAI4E,SAAS,CAACvE,KAAd,EAAqB;EACnBnE,MAAAA,GAAG,CAACvH,YAAJ,CAAiB,YAAjB,EAA+BiQ,SAAS,CAACvE,KAAzC;EACD,KAFD,MAEO;EACLnE,MAAAA,GAAG,CAAC2I,eAAJ,CAAoB,YAApB;EACD;;EAED,QAAI,OAAOD,SAAS,CAAC7E,SAAjB,KAA+B,QAAnC,EAA6C;EAC3C7D,MAAAA,GAAG,CAACvH,YAAJ,CAAiB,gBAAjB,EAAmCiQ,SAAS,CAAC7E,SAA7C;EACD,KAFD,MAEO;EACL7D,MAAAA,GAAG,CAAC2I,eAAJ,CAAoB,gBAApB;EACD;;EAED,QAAID,SAAS,CAAC1E,OAAd,EAAuB;EACrBhE,MAAAA,GAAG,CAACvH,YAAJ,CAAiB,cAAjB,EAAiC,EAAjC;EACD,KAFD,MAEO;EACLuH,MAAAA,GAAG,CAAC2I,eAAJ,CAAoB,cAApB;EACD;;EAED3I,IAAAA,GAAG,CAAC3H,KAAJ,CAAU4L,QAAV,GACE,OAAOyE,SAAS,CAACzE,QAAjB,KAA8B,QAA9B,GACOyE,SAAS,CAACzE,QADjB,UAEIyE,SAAS,CAACzE,QAHhB;;EAKA,QAAIyE,SAAS,CAACxE,IAAd,EAAoB;EAClBlE,MAAAA,GAAG,CAACvH,YAAJ,CAAiB,MAAjB,EAAyBiQ,SAAS,CAACxE,IAAnC;EACD,KAFD,MAEO;EACLlE,MAAAA,GAAG,CAAC2I,eAAJ,CAAoB,MAApB;EACD;;EAED,QACEF,SAAS,CAAC1E,OAAV,KAAsB2E,SAAS,CAAC3E,OAAhC,IACA0E,SAAS,CAAC7E,SAAV,KAAwB8E,SAAS,CAAC9E,SAFpC,EAGE;EACAkE,MAAAA,UAAU,CAAC/D,OAAD,EAAUzC,QAAQ,CAAC3C,KAAnB,CAAV;EACD;;EAED,QAAI+J,SAAS,CAAC5E,KAAd,EAAqB;EACnB,UAAI,CAACA,KAAL,EAAY;EACV9D,QAAAA,GAAG,CAAClH,WAAJ,CAAgB8O,kBAAkB,CAACc,SAAS,CAAC5E,KAAX,CAAlC;EACD,OAFD,MAEO,IAAI2E,SAAS,CAAC3E,KAAV,KAAoB4E,SAAS,CAAC5E,KAAlC,EAAyC;EAC9C9D,QAAAA,GAAG,CAAC4I,WAAJ,CAAgB9E,KAAhB;EACA9D,QAAAA,GAAG,CAAClH,WAAJ,CAAgB8O,kBAAkB,CAACc,SAAS,CAAC5E,KAAX,CAAlC;EACD;EACF,KAPD,MAOO,IAAIA,KAAJ,EAAW;EAChB9D,MAAAA,GAAG,CAAC4I,WAAJ,CAAgB9E,KAAhB;EACD;EACF;;EAED,SAAO;EACLkE,IAAAA,MAAM,EAANA,MADK;EAELQ,IAAAA,QAAQ,EAARA;EAFK,GAAP;EAID;EAGD;;EACAzC,MAAM,CAAC8C,OAAP,GAAiB,IAAjB;;ECjHA,IAAIC,SAAS,GAAG,CAAhB;EACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,EAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,EAAe,SAASC,WAAT,CACb3L,SADa,EAEbmJ,WAFa,EAGH;EACV,MAAM9H,KAAK,GAAGwI,aAAa,CAAC7J,SAAD,oBACtB+G,YADsB,EAEtBmC,sBAAsB,CAAC9J,oBAAoB,CAAC+J,WAAD,CAArB,CAFA,EAA3B,CADU;EAOV;EACA;;EACA,MAAIyC,WAAJ;EACA,MAAIC,WAAJ;EACA,MAAIC,0BAAJ;EACA,MAAIC,kBAAkB,GAAG,KAAzB;EACA,MAAIC,6BAA6B,GAAG,KAApC;EACA,MAAIC,YAAY,GAAG,KAAnB;EACA,MAAIC,mBAAmB,GAAG,KAA1B;EACA,MAAIC,gBAAJ;EACA,MAAIC,4BAAJ;EACA,MAAIC,aAAJ;EACA,MAAIC,SAA2B,GAAG,EAAlC;EACA,MAAIC,oBAAoB,GAAG7O,QAAQ,CAAC8O,WAAD,EAAcnL,KAAK,CAACoG,mBAApB,CAAnC;EACA,MAAIgF,aAAJ,CArBU;EAwBV;EACA;;EACA,MAAMC,EAAE,GAAGlB,SAAS,EAApB;EACA,MAAMmB,cAAc,GAAG,IAAvB;EACA,MAAMpE,OAAO,GAAGzJ,MAAM,CAACuC,KAAK,CAACkH,OAAP,CAAtB;EAEA,MAAM/H,KAAK,GAAG;EACZ;EACAoM,IAAAA,SAAS,EAAE,IAFC;EAGZ;EACA1I,IAAAA,SAAS,EAAE,KAJC;EAKZ;EACA2I,IAAAA,WAAW,EAAE,KAND;EAOZ;EACAC,IAAAA,SAAS,EAAE,KARC;EASZ;EACAC,IAAAA,OAAO,EAAE;EAVG,GAAd;EAaA,MAAM/I,QAAkB,GAAG;EACzB;EACA0I,IAAAA,EAAE,EAAFA,EAFyB;EAGzB1M,IAAAA,SAAS,EAATA,SAHyB;EAIzB0K,IAAAA,MAAM,EAAEjL,GAAG,EAJc;EAKzBkN,IAAAA,cAAc,EAAdA,cALyB;EAMzBtL,IAAAA,KAAK,EAALA,KANyB;EAOzBb,IAAAA,KAAK,EAALA,KAPyB;EAQzB+H,IAAAA,OAAO,EAAPA,OARyB;EASzB;EACAyE,IAAAA,kBAAkB,EAAlBA,kBAVyB;EAWzBC,IAAAA,QAAQ,EAARA,QAXyB;EAYzBzC,IAAAA,UAAU,EAAVA,UAZyB;EAazB0C,IAAAA,IAAI,EAAJA,IAbyB;EAczBC,IAAAA,IAAI,EAAJA,IAdyB;EAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;EAgBzBC,IAAAA,MAAM,EAANA,MAhByB;EAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;EAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;EAmBzBC,IAAAA,OAAO,EAAPA;EAnByB,GAA3B,CA3CU;EAkEV;;EACA;;EACA,MAAI,CAACnM,KAAK,CAACoH,MAAX,EAAmB;EACjB,IAAa;EACXjD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;EACD;;EAED,WAAOxB,QAAP;EACD,GA1ES;EA6EV;EACA;;;EACA,sBAA2B3C,KAAK,CAACoH,MAAN,CAAazE,QAAb,CAA3B;EAAA,MAAO0G,MAAP,iBAAOA,MAAP;EAAA,MAAeQ,QAAf,iBAAeA,QAAf;;EAEAR,EAAAA,MAAM,CAACvP,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;EACAuP,EAAAA,MAAM,CAACgC,EAAP,cAAoC1I,QAAQ,CAAC0I,EAA7C;EAEA1I,EAAAA,QAAQ,CAAC0G,MAAT,GAAkBA,MAAlB;EACA1K,EAAAA,SAAS,CAACD,MAAV,GAAmBiE,QAAnB;EACA0G,EAAAA,MAAM,CAAC3K,MAAP,GAAgBiE,QAAhB;EAEA,MAAMyJ,YAAY,GAAGlF,OAAO,CAACmF,GAAR,CAAY,UAACtE,MAAD;EAAA,WAAYA,MAAM,CAACzL,EAAP,CAAUqG,QAAV,CAAZ;EAAA,GAAZ,CAArB;EACA,MAAM2J,eAAe,GAAG3N,SAAS,CAAC4N,YAAV,CAAuB,eAAvB,CAAxB;EAEAC,EAAAA,YAAY;EACZC,EAAAA,2BAA2B;EAC3BC,EAAAA,YAAY;EAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAAChK,QAAD,CAAb,CAAV;;EAEA,MAAI3C,KAAK,CAACqH,YAAV,EAAwB;EACtBuF,IAAAA,YAAY;EACb,GAnGS;EAsGV;;;EACAvD,EAAAA,MAAM,CAAChH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;EAC1C,QAAIM,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IAA8BxD,QAAQ,CAACxD,KAAT,CAAe0D,SAAjD,EAA4D;EAC1DF,MAAAA,QAAQ,CAACgJ,kBAAT;EACD;EACF,GAJD;EAMAtC,EAAAA,MAAM,CAAChH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;EAC1C,QACEM,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IACAxD,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;EACA2Q,MAAAA,WAAW,GAAGxK,gBAAd,CAA+B,WAA/B,EAA4C6I,oBAA5C;EACD;EACF,GAPD;EASA,SAAOvI,QAAP,CAtHU;EAyHV;EACA;;EACA,WAASmK,0BAAT,GAAkE;EAChE,QAAOxF,KAAP,GAAgB3E,QAAQ,CAAC3C,KAAzB,CAAOsH,KAAP;EACA,WAAO3L,KAAK,CAACC,OAAN,CAAc0L,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;EACD;;EAED,WAASyF,wBAAT,GAA6C;EAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;EACD;;EAED,WAASE,oBAAT,GAAyC;EAAA;;EACvC;EACA,WAAO,CAAC,2BAACrK,QAAQ,CAAC3C,KAAT,CAAeoH,MAAhB,aAAC,sBAAuB8C,OAAxB,CAAR;EACD;;EAED,WAAS+C,gBAAT,GAAqC;EACnC,WAAO7B,aAAa,IAAIzM,SAAxB;EACD;;EAED,WAASkO,WAAT,GAAiC;EAC/B,QAAMnL,MAAM,GAAGuL,gBAAgB,GAAGC,UAAlC;EACA,WAAOxL,MAAM,GAAGtC,gBAAgB,CAACsC,MAAD,CAAnB,GAA8B/H,QAA3C;EACD;;EAED,WAASwT,0BAAT,GAAsD;EACpD,WAAO/D,WAAW,CAACC,MAAD,CAAlB;EACD;;EAED,WAAS+D,QAAT,CAAkBC,MAAlB,EAA2C;EACzC;EACA;EACA;EACA,QACG1K,QAAQ,CAACxD,KAAT,CAAesM,SAAf,IAA4B,CAAC9I,QAAQ,CAACxD,KAAT,CAAe0D,SAA7C,IACAb,YAAY,CAACC,OADb,IAEC6I,gBAAgB,IAAIA,gBAAgB,CAAC/O,IAAjB,KAA0B,OAHjD,EAIE;EACA,aAAO,CAAP;EACD;;EAED,WAAOR,uBAAuB,CAC5BoH,QAAQ,CAAC3C,KAAT,CAAe8F,KADa,EAE5BuH,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5B3H,YAAY,CAACI,KAHe,CAA9B;EAKD;;EAED,WAAS4G,YAAT,CAAsBY,QAAtB,EAA8C;EAAA,QAAxBA,QAAwB;EAAxBA,MAAAA,QAAwB,GAAb,KAAa;EAAA;;EAC5CjE,IAAAA,MAAM,CAAC3P,KAAP,CAAa6T,aAAb,GACE5K,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IAA8B,CAACmH,QAA/B,GAA0C,EAA1C,GAA+C,MADjD;EAEAjE,IAAAA,MAAM,CAAC3P,KAAP,CAAa+L,MAAb,QAAyB9C,QAAQ,CAAC3C,KAAT,CAAeyF,MAAxC;EACD;;EAED,WAASkH,UAAT,CACEa,IADF,EAEEpR,IAFF,EAGEqR,qBAHF,EAIQ;EAAA,QADNA,qBACM;EADNA,MAAAA,qBACM,GADkB,IAClB;EAAA;;EACNrB,IAAAA,YAAY,CAACrP,OAAb,CAAqB,UAAC2Q,WAAD,EAAiB;EACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;EACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,OAAAE,WAAW,EAAWtR,IAAX,CAAX;EACD;EACF,KAJD;;EAMA,QAAIqR,qBAAJ,EAA2B;EAAA;;EACzB,yBAAA9K,QAAQ,CAAC3C,KAAT,EAAewN,IAAf,yBAAwBpR,IAAxB;EACD;EACF;;EAED,WAASuR,0BAAT,GAA4C;EAC1C,QAAO/H,IAAP,GAAejD,QAAQ,CAAC3C,KAAxB,CAAO4F,IAAP;;EAEA,QAAI,CAACA,IAAI,CAACR,OAAV,EAAmB;EACjB;EACD;;EAED,QAAMwI,IAAI,aAAWhI,IAAI,CAACR,OAA1B;EACA,QAAMiG,EAAE,GAAGhC,MAAM,CAACgC,EAAlB;EACA,QAAMwC,KAAK,GAAGzQ,gBAAgB,CAACuF,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7I,SAAjC,CAA9B;EAEAkP,IAAAA,KAAK,CAAC9Q,OAAN,CAAc,UAAC2M,IAAD,EAAU;EACtB,UAAMoE,YAAY,GAAGpE,IAAI,CAACtB,YAAL,CAAkBwF,IAAlB,CAArB;;EAEA,UAAIjL,QAAQ,CAACxD,KAAT,CAAe0D,SAAnB,EAA8B;EAC5B6G,QAAAA,IAAI,CAAC5P,YAAL,CAAkB8T,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsBzC,EAAtB,GAA6BA,EAAjE;EACD,OAFD,MAEO;EACL,YAAM0C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAACzK,OAAb,CAAqBgI,EAArB,EAAyB,EAAzB,EAA6B/H,IAA7B,EAAlC;;EAEA,YAAIyK,SAAJ,EAAe;EACbrE,UAAAA,IAAI,CAAC5P,YAAL,CAAkB8T,IAAlB,EAAwBG,SAAxB;EACD,SAFD,MAEO;EACLrE,UAAAA,IAAI,CAACM,eAAL,CAAqB4D,IAArB;EACD;EACF;EACF,KAdD;EAeD;;EAED,WAASnB,2BAAT,GAA6C;EAC3C,QAAIH,eAAe,IAAI,CAAC3J,QAAQ,CAAC3C,KAAT,CAAe4F,IAAf,CAAoBC,QAA5C,EAAsD;EACpD;EACD;;EAED,QAAMgI,KAAK,GAAGzQ,gBAAgB,CAACuF,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7I,SAAjC,CAA9B;EAEAkP,IAAAA,KAAK,CAAC9Q,OAAN,CAAc,UAAC2M,IAAD,EAAU;EACtB,UAAI/G,QAAQ,CAAC3C,KAAT,CAAemG,WAAnB,EAAgC;EAC9BuD,QAAAA,IAAI,CAAC5P,YAAL,CACE,eADF,EAEE6I,QAAQ,CAACxD,KAAT,CAAe0D,SAAf,IAA4B6G,IAAI,KAAKuD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;EAMD,OAPD,MAOO;EACLvD,QAAAA,IAAI,CAACM,eAAL,CAAqB,eAArB;EACD;EACF,KAXD;EAYD;;EAED,WAASgE,gCAAT,GAAkD;EAChDnB,IAAAA,WAAW,GAAGrK,mBAAd,CAAkC,WAAlC,EAA+C0I,oBAA/C;EACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAClN,MAAnB,CACnB,UAACqE,QAAD;EAAA,aAAcA,QAAQ,KAAK2J,oBAA3B;EAAA,KADmB,CAArB;EAGD;;EAED,WAAS+C,eAAT,CAAyBvO,KAAzB,EAA+D;EAC7D;EACA,QAAIsC,YAAY,CAACC,OAAjB,EAA0B;EACxB,UAAI2I,YAAY,IAAIlL,KAAK,CAAC3D,IAAN,KAAe,WAAnC,EAAgD;EAC9C;EACD;EACF;;EAED,QAAMmS,YAAY,GACfxO,KAAK,CAACyO,YAAN,IAAsBzO,KAAK,CAACyO,YAAN,GAAqB,CAArB,CAAvB,IAAmDzO,KAAK,CAACkC,MAD3D,CAR6D;;EAY7D,QACEe,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IACA1E,cAAc,CAAC4H,MAAD,EAAS6E,YAAT,CAFhB,EAGE;EACA;EACD,KAjB4D;;;EAoB7D,QACE9Q,gBAAgB,CAACuF,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7I,SAAjC,CAAhB,CAA4DL,IAA5D,CAAiE,UAACU,EAAD;EAAA,aAC/DyC,cAAc,CAACzC,EAAD,EAAKkP,YAAL,CADiD;EAAA,KAAjE,CADF,EAIE;EACA,UAAIlM,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAED,UACEU,QAAQ,CAACxD,KAAT,CAAe0D,SAAf,IACAF,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;EACA;EACD;EACF,KAfD,MAeO;EACLyQ,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAAChK,QAAD,EAAWjD,KAAX,CAAnB,CAAV;EACD;;EAED,QAAIiD,QAAQ,CAAC3C,KAAT,CAAeiG,WAAf,KAA+B,IAAnC,EAAyC;EACvCtD,MAAAA,QAAQ,CAACgJ,kBAAT;EACAhJ,MAAAA,QAAQ,CAACmJ,IAAT,GAFuC;EAKvC;EACA;;EACAnB,MAAAA,6BAA6B,GAAG,IAAhC;EACAhO,MAAAA,UAAU,CAAC,YAAM;EACfgO,QAAAA,6BAA6B,GAAG,KAAhC;EACD,OAFS,CAAV,CARuC;EAavC;EACA;;EACA,UAAI,CAAChI,QAAQ,CAACxD,KAAT,CAAesM,SAApB,EAA+B;EAC7B2C,QAAAA,mBAAmB;EACpB;EACF;EACF;;EAED,WAASC,WAAT,GAA6B;EAC3BzD,IAAAA,YAAY,GAAG,IAAf;EACD;;EAED,WAAS0D,YAAT,GAA8B;EAC5B1D,IAAAA,YAAY,GAAG,KAAf;EACD;;EAED,WAAS2D,gBAAT,GAAkC;EAChC,QAAMC,GAAG,GAAG3B,WAAW,EAAvB;EACA2B,IAAAA,GAAG,CAACnM,gBAAJ,CAAqB,WAArB,EAAkC4L,eAAlC,EAAmD,IAAnD;EACAO,IAAAA,GAAG,CAACnM,gBAAJ,CAAqB,UAArB,EAAiC4L,eAAjC,EAAkDnT,aAAlD;EACA0T,IAAAA,GAAG,CAACnM,gBAAJ,CAAqB,YAArB,EAAmCiM,YAAnC,EAAiDxT,aAAjD;EACA0T,IAAAA,GAAG,CAACnM,gBAAJ,CAAqB,WAArB,EAAkCgM,WAAlC,EAA+CvT,aAA/C;EACD;;EAED,WAASsT,mBAAT,GAAqC;EACnC,QAAMI,GAAG,GAAG3B,WAAW,EAAvB;EACA2B,IAAAA,GAAG,CAAChM,mBAAJ,CAAwB,WAAxB,EAAqCyL,eAArC,EAAsD,IAAtD;EACAO,IAAAA,GAAG,CAAChM,mBAAJ,CAAwB,UAAxB,EAAoCyL,eAApC,EAAqDnT,aAArD;EACA0T,IAAAA,GAAG,CAAChM,mBAAJ,CAAwB,YAAxB,EAAsC8L,YAAtC,EAAoDxT,aAApD;EACA0T,IAAAA,GAAG,CAAChM,mBAAJ,CAAwB,WAAxB,EAAqC6L,WAArC,EAAkDvT,aAAlD;EACD;;EAED,WAAS2T,iBAAT,CAA2B1I,QAA3B,EAA6C2I,QAA7C,EAAyE;EACvEC,IAAAA,eAAe,CAAC5I,QAAD,EAAW,YAAM;EAC9B,UACE,CAACpD,QAAQ,CAACxD,KAAT,CAAe0D,SAAhB,IACAwG,MAAM,CAAC6D,UADP,IAEA7D,MAAM,CAAC6D,UAAP,CAAkBrL,QAAlB,CAA2BwH,MAA3B,CAHF,EAIE;EACAqF,QAAAA,QAAQ;EACT;EACF,KARc,CAAf;EASD;;EAED,WAASE,gBAAT,CAA0B7I,QAA1B,EAA4C2I,QAA5C,EAAwE;EACtEC,IAAAA,eAAe,CAAC5I,QAAD,EAAW2I,QAAX,CAAf;EACD;;EAED,WAASC,eAAT,CAAyB5I,QAAzB,EAA2C2I,QAA3C,EAAuE;EACrE,QAAMrN,GAAG,GAAG8L,0BAA0B,GAAG9L,GAAzC;;EAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;EAC9C,UAAIA,KAAK,CAACkC,MAAN,KAAiBP,GAArB,EAA0B;EACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;EACAmN,QAAAA,QAAQ;EACT;EACF,KARoE;EAWrE;;;EACA,QAAI3I,QAAQ,KAAK,CAAjB,EAAoB;EAClB,aAAO2I,QAAQ,EAAf;EACD;;EAEDtN,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgB0J,4BAAhB,CAA3B;EACA3J,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;EAEAwJ,IAAAA,4BAA4B,GAAGxJ,QAA/B;EACD;;EAED,WAASsN,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADuC,KACvC;EAAA;;EACN,QAAMnB,KAAK,GAAGzQ,gBAAgB,CAACuF,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7I,SAAjC,CAA9B;EACAkP,IAAAA,KAAK,CAAC9Q,OAAN,CAAc,UAAC2M,IAAD,EAAU;EACtBA,MAAAA,IAAI,CAACrH,gBAAL,CAAsByM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACA/D,MAAAA,SAAS,CAACzN,IAAV,CAAe;EAACkM,QAAAA,IAAI,EAAJA,IAAD;EAAOoF,QAAAA,SAAS,EAATA,SAAP;EAAkBC,QAAAA,OAAO,EAAPA,OAAlB;EAA2BC,QAAAA,OAAO,EAAPA;EAA3B,OAAf;EACD,KAHD;EAID;;EAED,WAASxC,YAAT,GAA8B;EAC5B,QAAIO,wBAAwB,EAA5B,EAAgC;EAC9B8B,MAAAA,EAAE,CAAC,YAAD,EAAe9H,SAAf,EAA0B;EAAChM,QAAAA,OAAO,EAAE;EAAV,OAA1B,CAAF;EACA8T,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;EAAClU,QAAAA,OAAO,EAAE;EAAV,OAA5C,CAAF;EACD;;EAEDiC,IAAAA,aAAa,CAAC2F,QAAQ,CAAC3C,KAAT,CAAeuH,OAAhB,CAAb,CAAsCxK,OAAtC,CAA8C,UAAC+R,SAAD,EAAe;EAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;EAC1B;EACD;;EAEDD,MAAAA,EAAE,CAACC,SAAD,EAAY/H,SAAZ,CAAF;;EAEA,cAAQ+H,SAAR;EACE,aAAK,YAAL;EACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;EACA;;EACF,aAAK,OAAL;EACEJ,UAAAA,EAAE,CAACvU,MAAM,GAAG,UAAH,GAAgB,MAAvB,EAA+B4U,gBAA/B,CAAF;EACA;;EACF,aAAK,SAAL;EACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;EACA;EATJ;EAWD,KAlBD;EAmBD;;EAED,WAASC,eAAT,GAAiC;EAC/BlE,IAAAA,SAAS,CAAClO,OAAV,CAAkB,gBAAyD;EAAA,UAAvD2M,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDoF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEtF,MAAAA,IAAI,CAAClH,mBAAL,CAAyBsM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGA/D,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAASlE,SAAT,CAAmBrH,KAAnB,EAAuC;EAAA;;EACrC,QAAI0P,uBAAuB,GAAG,KAA9B;;EAEA,QACE,CAACzM,QAAQ,CAACxD,KAAT,CAAeoM,SAAhB,IACA8D,sBAAsB,CAAC3P,KAAD,CADtB,IAEAiL,6BAHF,EAIE;EACA;EACD;;EAED,QAAM2E,UAAU,GAAG,sBAAAxE,gBAAgB,SAAhB,8BAAkB/O,IAAlB,MAA2B,OAA9C;EAEA+O,IAAAA,gBAAgB,GAAGpL,KAAnB;EACA0L,IAAAA,aAAa,GAAG1L,KAAK,CAAC0L,aAAtB;EAEAqB,IAAAA,2BAA2B;;EAE3B,QAAI,CAAC9J,QAAQ,CAACxD,KAAT,CAAe0D,SAAhB,IAA6BrE,YAAY,CAACkB,KAAD,CAA7C,EAAsD;EACpD;EACA;EACA;EACA;EACA0K,MAAAA,kBAAkB,CAACrN,OAAnB,CAA2B,UAACwE,QAAD;EAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;EAAA,OAA3B;EACD,KAxBoC;;;EA2BrC,QACEA,KAAK,CAAC3D,IAAN,KAAe,OAAf,KACC4G,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCwO,kBAFF,KAGA/H,QAAQ,CAAC3C,KAAT,CAAeiG,WAAf,KAA+B,KAH/B,IAIAtD,QAAQ,CAACxD,KAAT,CAAe0D,SALjB,EAME;EACAuM,MAAAA,uBAAuB,GAAG,IAA1B;EACD,KARD,MAQO;EACLxC,MAAAA,YAAY,CAAClN,KAAD,CAAZ;EACD;;EAED,QAAIA,KAAK,CAAC3D,IAAN,KAAe,OAAnB,EAA4B;EAC1B2O,MAAAA,kBAAkB,GAAG,CAAC0E,uBAAtB;EACD;;EAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;EAC1CC,MAAAA,YAAY,CAAC7P,KAAD,CAAZ;EACD;EACF;;EAED,WAASyL,WAAT,CAAqBzL,KAArB,EAA8C;EAC5C,QAAMkC,MAAM,GAAGlC,KAAK,CAACkC,MAArB;EACA,QAAM4N,6BAA6B,GACjCvC,gBAAgB,GAAGpL,QAAnB,CAA4BD,MAA5B,KAAuCyH,MAAM,CAACxH,QAAP,CAAgBD,MAAhB,CADzC;;EAGA,QAAIlC,KAAK,CAAC3D,IAAN,KAAe,WAAf,IAA8ByT,6BAAlC,EAAiE;EAC/D;EACD;;EAED,QAAM/P,cAAc,GAAGgQ,mBAAmB,GACvCpS,MADoB,CACbgM,MADa,EAEpBgD,GAFoB,CAEhB,UAAChD,MAAD,EAAY;EAAA;;EACf,UAAM1G,QAAQ,GAAG0G,MAAM,CAAC3K,MAAxB;EACA,UAAMS,KAAK,4BAAGwD,QAAQ,CAAC2I,cAAZ,qBAAG,sBAAyBnM,KAAvC;;EAEA,UAAIA,KAAJ,EAAW;EACT,eAAO;EACLW,UAAAA,UAAU,EAAEuJ,MAAM,CAACqG,qBAAP,EADP;EAEL3P,UAAAA,WAAW,EAAEZ,KAFR;EAGLa,UAAAA,KAAK,EAALA;EAHK,SAAP;EAKD;;EAED,aAAO,IAAP;EACD,KAfoB,EAgBpB9C,MAhBoB,CAgBbC,OAhBa,CAAvB;;EAkBA,QAAIqC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;EAC3DsO,MAAAA,gCAAgC;EAChCuB,MAAAA,YAAY,CAAC7P,KAAD,CAAZ;EACD;EACF;;EAED,WAASuP,YAAT,CAAsBvP,KAAtB,EAA+C;EAC7C,QAAMiQ,UAAU,GACdN,sBAAsB,CAAC3P,KAAD,CAAtB,IACCiD,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDwO,kBAFnD;;EAIA,QAAIiF,UAAJ,EAAgB;EACd;EACD;;EAED,QAAIhN,QAAQ,CAAC3C,KAAT,CAAemG,WAAnB,EAAgC;EAC9BxD,MAAAA,QAAQ,CAACoJ,qBAAT,CAA+BrM,KAA/B;EACA;EACD;;EAED6P,IAAAA,YAAY,CAAC7P,KAAD,CAAZ;EACD;;EAED,WAASwP,gBAAT,CAA0BxP,KAA1B,EAAmD;EACjD,QACEiD,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACAwD,KAAK,CAACkC,MAAN,KAAiBqL,gBAAgB,EAFnC,EAGE;EACA;EACD,KANgD;;;EASjD,QACEtK,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IACAzG,KAAK,CAACkQ,aADN,IAEAvG,MAAM,CAACxH,QAAP,CAAgBnC,KAAK,CAACkQ,aAAtB,CAHF,EAIE;EACA;EACD;;EAEDL,IAAAA,YAAY,CAAC7P,KAAD,CAAZ;EACD;;EAED,WAAS2P,sBAAT,CAAgC3P,KAAhC,EAAuD;EACrD,WAAOsC,YAAY,CAACC,OAAb,GACH8K,wBAAwB,OAAOrN,KAAK,CAAC3D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;EAGD;;EAED,WAAS2T,oBAAT,GAAsC;EACpCC,IAAAA,qBAAqB;EAErB,2BAMInN,QAAQ,CAAC3C,KANb;EAAA,QACEmH,aADF,oBACEA,aADF;EAAA,QAEEvJ,SAFF,oBAEEA,SAFF;EAAA,QAGEyC,MAHF,oBAGEA,MAHF;EAAA,QAIE2F,sBAJF,oBAIEA,sBAJF;EAAA,QAKEK,cALF,oBAKEA,cALF;EAQA,QAAMlB,KAAK,GAAG6H,oBAAoB,KAAK5D,WAAW,CAACC,MAAD,CAAX,CAAoBlE,KAAzB,GAAiC,IAAnE;EAEA,QAAM4K,iBAAiB,GAAG/J,sBAAsB,GAC5C;EACE0J,MAAAA,qBAAqB,EAAE1J,sBADzB;EAEEgK,MAAAA,cAAc,EACZhK,sBAAsB,CAACgK,cAAvB,IAAyC/C,gBAAgB;EAH7D,KAD4C,GAM5CtO,SANJ;EAQA,QAAMsR,aAA2D,GAAG;EAClEjI,MAAAA,IAAI,EAAE,SAD4D;EAElEkI,MAAAA,OAAO,EAAE,IAFyD;EAGlEC,MAAAA,KAAK,EAAE,aAH2D;EAIlEC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJwD;EAKlE9T,MAAAA,EALkE,qBAKtD;EAAA,YAAR6C,KAAQ,SAARA,KAAQ;;EACV,YAAI6N,oBAAoB,EAAxB,EAA4B;EAC1B,sCAAcG,0BAA0B,EAAxC;EAAA,cAAO9L,GAAP,yBAAOA,GAAP;;EAEA,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6CtE,OAA7C,CAAqD,UAAC6Q,IAAD,EAAU;EAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;EACxBvM,cAAAA,GAAG,CAACvH,YAAJ,CAAiB,gBAAjB,EAAmCqF,KAAK,CAACvB,SAAzC;EACD,aAFD,MAEO;EACL,kBAAIuB,KAAK,CAACkR,UAAN,CAAiBhH,MAAjB,kBAAuCuE,IAAvC,CAAJ,EAAoD;EAClDvM,gBAAAA,GAAG,CAACvH,YAAJ,WAAyB8T,IAAzB,EAAiC,EAAjC;EACD,eAFD,MAEO;EACLvM,gBAAAA,GAAG,CAAC2I,eAAJ,WAA4B4D,IAA5B;EACD;EACF;EACF,WAVD;EAYAzO,UAAAA,KAAK,CAACkR,UAAN,CAAiBhH,MAAjB,GAA0B,EAA1B;EACD;EACF;EAvBiE,KAApE;EA6BA,QAAMiH,SAAmC,GAAG,CAC1C;EACEtI,MAAAA,IAAI,EAAE,QADR;EAEEgH,MAAAA,OAAO,EAAE;EACP3O,QAAAA,MAAM,EAANA;EADO;EAFX,KAD0C,EAO1C;EACE2H,MAAAA,IAAI,EAAE,iBADR;EAEEgH,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EACPhQ,UAAAA,GAAG,EAAE,CADE;EAEPG,UAAAA,MAAM,EAAE,CAFD;EAGPE,UAAAA,IAAI,EAAE,CAHC;EAIPG,UAAAA,KAAK,EAAE;EAJA;EADF;EAFX,KAP0C,EAkB1C;EACEiH,MAAAA,IAAI,EAAE,MADR;EAEEgH,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EADF;EAFX,KAlB0C,EAwB1C;EACEvI,MAAAA,IAAI,EAAE,eADR;EAEEgH,MAAAA,OAAO,EAAE;EACPwB,QAAAA,QAAQ,EAAE,CAACnK;EADJ;EAFX,KAxB0C,EA8B1C4J,aA9B0C,CAA5C;;EAiCA,QAAIjD,oBAAoB,MAAM7H,KAA9B,EAAqC;EACnCmL,MAAAA,SAAS,CAAC9S,IAAV,CAAe;EACbwK,QAAAA,IAAI,EAAE,OADO;EAEbgH,QAAAA,OAAO,EAAE;EACP1P,UAAAA,OAAO,EAAE6F,KADF;EAEPoL,UAAAA,OAAO,EAAE;EAFF;EAFI,OAAf;EAOD;;EAEDD,IAAAA,SAAS,CAAC9S,IAAV,OAAA8S,SAAS,EAAU,CAAAnJ,aAAa,QAAb,YAAAA,aAAa,CAAEmJ,SAAf,KAA4B,EAAtC,CAAT;EAEA3N,IAAAA,QAAQ,CAAC2I,cAAT,GAA0BmF,iBAAY,CACpCV,iBADoC,EAEpC1G,MAFoC,oBAI/BlC,aAJ+B;EAKlCvJ,MAAAA,SAAS,EAATA,SALkC;EAMlCoN,MAAAA,aAAa,EAAbA,aANkC;EAOlCsF,MAAAA,SAAS,EAATA;EAPkC,OAAtC;EAUD;;EAED,WAASR,qBAAT,GAAuC;EACrC,QAAInN,QAAQ,CAAC2I,cAAb,EAA6B;EAC3B3I,MAAAA,QAAQ,CAAC2I,cAAT,CAAwBa,OAAxB;EACAxJ,MAAAA,QAAQ,CAAC2I,cAAT,GAA0B,IAA1B;EACD;EACF;;EAED,WAASoF,KAAT,GAAuB;EACrB,QAAO/K,QAAP,GAAmBhD,QAAQ,CAAC3C,KAA5B,CAAO2F,QAAP;EAEA,QAAIuH,UAAJ,CAHqB;EAMrB;EACA;EACA;EACA;;EACA,QAAMxD,IAAI,GAAGuD,gBAAgB,EAA7B;;EAEA,QACGtK,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IAA8BR,QAAQ,KAAK1K,uBAA5C,IACA0K,QAAQ,KAAK,QAFf,EAGE;EACAuH,MAAAA,UAAU,GAAGxD,IAAI,CAACwD,UAAlB;EACD,KALD,MAKO;EACLA,MAAAA,UAAU,GAAG/Q,sBAAsB,CAACwJ,QAAD,EAAW,CAAC+D,IAAD,CAAX,CAAnC;EACD,KAnBoB;EAsBrB;;;EACA,QAAI,CAACwD,UAAU,CAACrL,QAAX,CAAoBwH,MAApB,CAAL,EAAkC;EAChC6D,MAAAA,UAAU,CAAC/S,WAAX,CAAuBkP,MAAvB;EACD;;EAED1G,IAAAA,QAAQ,CAACxD,KAAT,CAAesM,SAAf,GAA2B,IAA3B;EAEAoE,IAAAA,oBAAoB;EAEpB;;EACA,IAAa;EACX;EACAhM,MAAAA,QAAQ,CACNlB,QAAQ,CAAC3C,KAAT,CAAemG,WAAf,IACER,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEE+D,IAAI,CAACiH,kBAAL,KAA4BtH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaEpG,IAbF,CAaO,GAbP,CAJM,CAAR;EAmBD;EACF;;EAED,WAASwM,mBAAT,GAAgD;EAC9C,WAAO5R,SAAS,CACdwL,MAAM,CAACxK,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;EAGD;;EAED,WAAS+N,YAAT,CAAsBlN,KAAtB,EAA2C;EACzCiD,IAAAA,QAAQ,CAACgJ,kBAAT;;EAEA,QAAIjM,KAAJ,EAAW;EACTiN,MAAAA,UAAU,CAAC,WAAD,EAAc,CAAChK,QAAD,EAAWjD,KAAX,CAAd,CAAV;EACD;;EAED6O,IAAAA,gBAAgB;EAEhB,QAAIzI,KAAK,GAAGsH,QAAQ,CAAC,IAAD,CAApB;;EACA,gCAAiCN,0BAA0B,EAA3D;EAAA,QAAO8D,UAAP;EAAA,QAAmBC,UAAnB;;EAEA,QAAI7O,YAAY,CAACC,OAAb,IAAwB2O,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;EAC/D/K,MAAAA,KAAK,GAAG+K,UAAR;EACD;;EAED,QAAI/K,KAAJ,EAAW;EACTyE,MAAAA,WAAW,GAAG5N,UAAU,CAAC,YAAM;EAC7BgG,QAAAA,QAAQ,CAACkJ,IAAT;EACD,OAFuB,EAErB/F,KAFqB,CAAxB;EAGD,KAJD,MAIO;EACLnD,MAAAA,QAAQ,CAACkJ,IAAT;EACD;EACF;;EAED,WAAS0D,YAAT,CAAsB7P,KAAtB,EAA0C;EACxCiD,IAAAA,QAAQ,CAACgJ,kBAAT;EAEAgB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAAChK,QAAD,EAAWjD,KAAX,CAAhB,CAAV;;EAEA,QAAI,CAACiD,QAAQ,CAACxD,KAAT,CAAe0D,SAApB,EAA+B;EAC7BuL,MAAAA,mBAAmB;EAEnB;EACD,KATuC;EAYxC;EACA;EACA;;;EACA,QACEzL,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACAyG,QAAQ,CAAC3C,KAAT,CAAeuH,OAAf,CAAuBrL,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoCwD,KAAK,CAAC3D,IAA1C,KAAmD,CAFnD,IAGA2O,kBAJF,EAKE;EACA;EACD;;EAED,QAAM5E,KAAK,GAAGsH,QAAQ,CAAC,KAAD,CAAtB;;EAEA,QAAItH,KAAJ,EAAW;EACT0E,MAAAA,WAAW,GAAG7N,UAAU,CAAC,YAAM;EAC7B,YAAIgG,QAAQ,CAACxD,KAAT,CAAe0D,SAAnB,EAA8B;EAC5BF,UAAAA,QAAQ,CAACmJ,IAAT;EACD;EACF,OAJuB,EAIrBhG,KAJqB,CAAxB;EAKD,KAND,MAMO;EACL;EACA;EACA2E,MAAAA,0BAA0B,GAAGqG,qBAAqB,CAAC,YAAM;EACvDnO,QAAAA,QAAQ,CAACmJ,IAAT;EACD,OAFiD,CAAlD;EAGD;EACF,GA3wBS;EA8wBV;EACA;;;EACA,WAASE,MAAT,GAAwB;EACtBrJ,IAAAA,QAAQ,CAACxD,KAAT,CAAeoM,SAAf,GAA2B,IAA3B;EACD;;EAED,WAASU,OAAT,GAAyB;EACvB;EACA;EACAtJ,IAAAA,QAAQ,CAACmJ,IAAT;EACAnJ,IAAAA,QAAQ,CAACxD,KAAT,CAAeoM,SAAf,GAA2B,KAA3B;EACD;;EAED,WAASI,kBAAT,GAAoC;EAClCjP,IAAAA,YAAY,CAAC6N,WAAD,CAAZ;EACA7N,IAAAA,YAAY,CAAC8N,WAAD,CAAZ;EACAuG,IAAAA,oBAAoB,CAACtG,0BAAD,CAApB;EACD;;EAED,WAASmB,QAAT,CAAkBjE,YAAlB,EAAsD;EACpD;EACA,IAAa;EACX9D,MAAAA,QAAQ,CAAClB,QAAQ,CAACxD,KAAT,CAAeqM,WAAhB,EAA6BzI,uBAAuB,CAAC,UAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACxD,KAAT,CAAeqM,WAAnB,EAAgC;EAC9B;EACD;;EAEDmB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAAChK,QAAD,EAAWgF,YAAX,CAAnB,CAAV;EAEAwH,IAAAA,eAAe;EAEf,QAAMrF,SAAS,GAAGnH,QAAQ,CAAC3C,KAA3B;EACA,QAAM+J,SAAS,GAAGvB,aAAa,CAAC7J,SAAD,oBAC1BmL,SAD0B,EAE1B/L,oBAAoB,CAAC4J,YAAD,CAFM;EAG7BzB,MAAAA,gBAAgB,EAAE;EAHW,OAA/B;EAMAvD,IAAAA,QAAQ,CAAC3C,KAAT,GAAiB+J,SAAjB;EAEAyC,IAAAA,YAAY;;EAEZ,QAAI1C,SAAS,CAAC1D,mBAAV,KAAkC2D,SAAS,CAAC3D,mBAAhD,EAAqE;EACnE4H,MAAAA,gCAAgC;EAChC9C,MAAAA,oBAAoB,GAAG7O,QAAQ,CAC7B8O,WAD6B,EAE7BpB,SAAS,CAAC3D,mBAFmB,CAA/B;EAID,KA/BmD;;;EAkCpD,QAAI0D,SAAS,CAACtC,aAAV,IAA2B,CAACuC,SAAS,CAACvC,aAA1C,EAAyD;EACvDpK,MAAAA,gBAAgB,CAAC0M,SAAS,CAACtC,aAAX,CAAhB,CAA0CzK,OAA1C,CAAkD,UAAC2M,IAAD,EAAU;EAC1DA,QAAAA,IAAI,CAACM,eAAL,CAAqB,eAArB;EACD,OAFD;EAGD,KAJD,MAIO,IAAID,SAAS,CAACvC,aAAd,EAA6B;EAClC7I,MAAAA,SAAS,CAACqL,eAAV,CAA0B,eAA1B;EACD;;EAEDyC,IAAAA,2BAA2B;EAC3BC,IAAAA,YAAY;;EAEZ,QAAI7C,QAAJ,EAAc;EACZA,MAAAA,QAAQ,CAACC,SAAD,EAAYC,SAAZ,CAAR;EACD;;EAED,QAAIpH,QAAQ,CAAC2I,cAAb,EAA6B;EAC3BuE,MAAAA,oBAAoB,GADO;EAI3B;EACA;EACA;;EACAJ,MAAAA,mBAAmB,GAAG1S,OAAtB,CAA8B,UAACiU,YAAD,EAAkB;EAC9C;EACA;EACAF,QAAAA,qBAAqB,CAACE,YAAY,CAACtS,MAAb,CAAqB4M,cAArB,CAAqC2F,WAAtC,CAArB;EACD,OAJD;EAKD;;EAEDtE,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAAChK,QAAD,EAAWgF,YAAX,CAAlB,CAAV;EACD;;EAED,WAASwB,UAAT,CAAoB/D,OAApB,EAA4C;EAC1CzC,IAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAACxG,MAAAA,OAAO,EAAPA;EAAD,KAAlB;EACD;;EAED,WAASyG,IAAT,GAAsB;EACpB;EACA,IAAa;EACXhI,MAAAA,QAAQ,CAAClB,QAAQ,CAACxD,KAAT,CAAeqM,WAAhB,EAA6BzI,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAMmO,gBAAgB,GAAGvO,QAAQ,CAACxD,KAAT,CAAe0D,SAAxC;EACA,QAAM2I,WAAW,GAAG7I,QAAQ,CAACxD,KAAT,CAAeqM,WAAnC;EACA,QAAM2F,UAAU,GAAG,CAACxO,QAAQ,CAACxD,KAAT,CAAeoM,SAAnC;EACA,QAAM6F,uBAAuB,GAC3BpP,YAAY,CAACC,OAAb,IAAwB,CAACU,QAAQ,CAAC3C,KAAT,CAAesH,KAD1C;EAEA,QAAMvB,QAAQ,GAAGxK,uBAAuB,CACtCoH,QAAQ,CAAC3C,KAAT,CAAe+F,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;EAMA,QACEmL,gBAAgB,IAChB1F,WADA,IAEA2F,UAFA,IAGAC,uBAJF,EAKE;EACA;EACD,KAzBmB;EA4BpB;EACA;;;EACA,QAAInE,gBAAgB,GAAGV,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;EAC/C;EACD;;EAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAAChK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAAC3C,KAAT,CAAe6G,MAAf,CAAsBlE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAACxD,KAAT,CAAe0D,SAAf,GAA2B,IAA3B;;EAEA,QAAImK,oBAAoB,EAAxB,EAA4B;EAC1B3D,MAAAA,MAAM,CAAC3P,KAAP,CAAa2X,UAAb,GAA0B,SAA1B;EACD;;EAED3E,IAAAA,YAAY;EACZ6B,IAAAA,gBAAgB;;EAEhB,QAAI,CAAC5L,QAAQ,CAACxD,KAAT,CAAesM,SAApB,EAA+B;EAC7BpC,MAAAA,MAAM,CAAC3P,KAAP,CAAa4X,UAAb,GAA0B,MAA1B;EACD,KAlDmB;EAqDpB;;;EACA,QAAItE,oBAAoB,EAAxB,EAA4B;EAC1B,mCAAuBG,0BAA0B,EAAjD;EAAA,UAAO9L,GAAP,0BAAOA,GAAP;EAAA,UAAY+D,OAAZ,0BAAYA,OAAZ;;EACAtG,MAAAA,qBAAqB,CAAC,CAACuC,GAAD,EAAM+D,OAAN,CAAD,EAAiB,CAAjB,CAArB;EACD;;EAED4F,IAAAA,aAAa,GAAG,yBAAY;EAAA;;EAC1B,UAAI,CAACrI,QAAQ,CAACxD,KAAT,CAAe0D,SAAhB,IAA6BgI,mBAAjC,EAAsD;EACpD;EACD;;EAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;EAQ1B,WAAKxB,MAAM,CAACkI,YAAZ;EAEAlI,MAAAA,MAAM,CAAC3P,KAAP,CAAa4X,UAAb,GAA0B3O,QAAQ,CAAC3C,KAAT,CAAeqG,cAAzC;;EAEA,UAAI2G,oBAAoB,MAAMrK,QAAQ,CAAC3C,KAAT,CAAekF,SAA7C,EAAwD;EACtD,qCAAuBiI,0BAA0B,EAAjD;EAAA,YAAO9L,IAAP,0BAAOA,GAAP;EAAA,YAAY+D,QAAZ,0BAAYA,OAAZ;;EACAtG,QAAAA,qBAAqB,CAAC,CAACuC,IAAD,EAAM+D,QAAN,CAAD,EAAiBW,QAAjB,CAArB;EACA7G,QAAAA,kBAAkB,CAAC,CAACmC,IAAD,EAAM+D,QAAN,CAAD,EAAiB,SAAjB,CAAlB;EACD;;EAEDuI,MAAAA,0BAA0B;EAC1BlB,MAAAA,2BAA2B;EAE3BnP,MAAAA,YAAY,CAAC+M,gBAAD,EAAmB1H,QAAnB,CAAZ,CArB0B;EAwB1B;;EACA,gCAAAA,QAAQ,CAAC2I,cAAT,4CAAyB2F,WAAzB;EAEAtE,MAAAA,UAAU,CAAC,SAAD,EAAY,CAAChK,QAAD,CAAZ,CAAV;;EAEA,UAAIA,QAAQ,CAAC3C,KAAT,CAAekF,SAAf,IAA4B8H,oBAAoB,EAApD,EAAwD;EACtD4B,QAAAA,gBAAgB,CAAC7I,QAAD,EAAW,YAAM;EAC/BpD,UAAAA,QAAQ,CAACxD,KAAT,CAAeuM,OAAf,GAAyB,IAAzB;EACAiB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAAChK,QAAD,CAAZ,CAAV;EACD,SAHe,CAAhB;EAID;EACF,KAnCD;;EAqCA+N,IAAAA,KAAK;EACN;;EAED,WAAS5E,IAAT,GAAsB;EACpB;EACA,IAAa;EACXjI,MAAAA,QAAQ,CAAClB,QAAQ,CAACxD,KAAT,CAAeqM,WAAhB,EAA6BzI,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAMyO,eAAe,GAAG,CAAC7O,QAAQ,CAACxD,KAAT,CAAe0D,SAAxC;EACA,QAAM2I,WAAW,GAAG7I,QAAQ,CAACxD,KAAT,CAAeqM,WAAnC;EACA,QAAM2F,UAAU,GAAG,CAACxO,QAAQ,CAACxD,KAAT,CAAeoM,SAAnC;EACA,QAAMxF,QAAQ,GAAGxK,uBAAuB,CACtCoH,QAAQ,CAAC3C,KAAT,CAAe+F,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;EAMA,QAAIyL,eAAe,IAAIhG,WAAnB,IAAkC2F,UAAtC,EAAkD;EAChD;EACD;;EAEDxE,IAAAA,UAAU,CAAC,QAAD,EAAW,CAAChK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAAC3C,KAAT,CAAe2G,MAAf,CAAsBhE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAACxD,KAAT,CAAe0D,SAAf,GAA2B,KAA3B;EACAF,IAAAA,QAAQ,CAACxD,KAAT,CAAeuM,OAAf,GAAyB,KAAzB;EACAb,IAAAA,mBAAmB,GAAG,KAAtB;EACAH,IAAAA,kBAAkB,GAAG,KAArB;;EAEA,QAAIsC,oBAAoB,EAAxB,EAA4B;EAC1B3D,MAAAA,MAAM,CAAC3P,KAAP,CAAa2X,UAAb,GAA0B,QAA1B;EACD;;EAEDrD,IAAAA,gCAAgC;EAChCI,IAAAA,mBAAmB;EACnB1B,IAAAA,YAAY,CAAC,IAAD,CAAZ;;EAEA,QAAIM,oBAAoB,EAAxB,EAA4B;EAC1B,mCAAuBG,0BAA0B,EAAjD;EAAA,UAAO9L,GAAP,0BAAOA,GAAP;EAAA,UAAY+D,OAAZ,0BAAYA,OAAZ;;EAEA,UAAIzC,QAAQ,CAAC3C,KAAT,CAAekF,SAAnB,EAA8B;EAC5BpG,QAAAA,qBAAqB,CAAC,CAACuC,GAAD,EAAM+D,OAAN,CAAD,EAAiBW,QAAjB,CAArB;EACA7G,QAAAA,kBAAkB,CAAC,CAACmC,GAAD,EAAM+D,OAAN,CAAD,EAAiB,QAAjB,CAAlB;EACD;EACF;;EAEDuI,IAAAA,0BAA0B;EAC1BlB,IAAAA,2BAA2B;;EAE3B,QAAI9J,QAAQ,CAAC3C,KAAT,CAAekF,SAAnB,EAA8B;EAC5B,UAAI8H,oBAAoB,EAAxB,EAA4B;EAC1ByB,QAAAA,iBAAiB,CAAC1I,QAAD,EAAWpD,QAAQ,CAACuJ,OAApB,CAAjB;EACD;EACF,KAJD,MAIO;EACLvJ,MAAAA,QAAQ,CAACuJ,OAAT;EACD;EACF;;EAED,WAASH,qBAAT,CAA+BrM,KAA/B,EAAwD;EACtD;EACA,IAAa;EACXmE,MAAAA,QAAQ,CACNlB,QAAQ,CAACxD,KAAT,CAAeqM,WADT,EAENzI,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;EAID;;EAED8J,IAAAA,WAAW,GAAGxK,gBAAd,CAA+B,WAA/B,EAA4C6I,oBAA5C;EACA5N,IAAAA,YAAY,CAAC8M,kBAAD,EAAqBc,oBAArB,CAAZ;EACAA,IAAAA,oBAAoB,CAACxL,KAAD,CAApB;EACD;;EAED,WAASwM,OAAT,GAAyB;EACvB;EACA,IAAa;EACXrI,MAAAA,QAAQ,CAAClB,QAAQ,CAACxD,KAAT,CAAeqM,WAAhB,EAA6BzI,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACxD,KAAT,CAAe0D,SAAnB,EAA8B;EAC5BF,MAAAA,QAAQ,CAACmJ,IAAT;EACD;;EAED,QAAI,CAACnJ,QAAQ,CAACxD,KAAT,CAAesM,SAApB,EAA+B;EAC7B;EACD;;EAEDqE,IAAAA,qBAAqB,GAdE;EAiBvB;EACA;;EACAL,IAAAA,mBAAmB,GAAG1S,OAAtB,CAA8B,UAACiU,YAAD,EAAkB;EAC9CA,MAAAA,YAAY,CAACtS,MAAb,CAAqBwN,OAArB;EACD,KAFD;;EAIA,QAAI7C,MAAM,CAAC6D,UAAX,EAAuB;EACrB7D,MAAAA,MAAM,CAAC6D,UAAP,CAAkBjD,WAAlB,CAA8BZ,MAA9B;EACD;;EAEDgB,IAAAA,gBAAgB,GAAGA,gBAAgB,CAACnN,MAAjB,CAAwB,UAACuU,CAAD;EAAA,aAAOA,CAAC,KAAK9O,QAAb;EAAA,KAAxB,CAAnB;EAEAA,IAAAA,QAAQ,CAACxD,KAAT,CAAesM,SAAf,GAA2B,KAA3B;EACAkB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAAChK,QAAD,CAAb,CAAV;EACD;;EAED,WAASwJ,OAAT,GAAyB;EACvB;EACA,IAAa;EACXtI,MAAAA,QAAQ,CAAClB,QAAQ,CAACxD,KAAT,CAAeqM,WAAhB,EAA6BzI,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACxD,KAAT,CAAeqM,WAAnB,EAAgC;EAC9B;EACD;;EAED7I,IAAAA,QAAQ,CAACgJ,kBAAT;EACAhJ,IAAAA,QAAQ,CAACuJ,OAAT;EAEAiD,IAAAA,eAAe;EAEf,WAAOxQ,SAAS,CAACD,MAAjB;EAEAiE,IAAAA,QAAQ,CAACxD,KAAT,CAAeqM,WAAf,GAA6B,IAA7B;EAEAmB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAAChK,QAAD,CAAd,CAAV;EACD;EACF;;EC/mCD,SAAS+O,KAAT,CACEpN,OADF,EAEEqN,aAFF,EAGyB;EAAA,MADvBA,aACuB;EADvBA,IAAAA,aACuB,GADS,EACT;EAAA;;EACvB,MAAMzK,OAAO,GAAGxB,YAAY,CAACwB,OAAb,CAAqB7J,MAArB,CAA4BsU,aAAa,CAACzK,OAAd,IAAyB,EAArD,CAAhB;EAEA;;EACA,EAAa;EACX7C,IAAAA,eAAe,CAACC,OAAD,CAAf;EACAsD,IAAAA,aAAa,CAAC+J,aAAD,EAAgBzK,OAAhB,CAAb;EACD;;EAEDpE,EAAAA,wBAAwB;EAExB,MAAMgF,WAA2B,qBAAO6J,aAAP;EAAsBzK,IAAAA,OAAO,EAAPA;EAAtB,IAAjC;EAEA,MAAM0K,QAAQ,GAAGhT,kBAAkB,CAAC0F,OAAD,CAAnC;EAEA;;EACA,EAAa;EACX,QAAMuN,sBAAsB,GAAGxT,SAAS,CAACyJ,WAAW,CAAC1C,OAAb,CAAxC;EACA,QAAM0M,6BAA6B,GAAGF,QAAQ,CAAC/I,MAAT,GAAkB,CAAxD;EACAhF,IAAAA,QAAQ,CACNgO,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUE7O,IAVF,CAUO,GAVP,CAFM,CAAR;EAcD;;EAED,MAAM8O,SAAS,GAAGH,QAAQ,CAAC3T,MAAT,CAChB,UAACC,GAAD,EAAMS,SAAN,EAAgC;EAC9B,QAAMgE,QAAQ,GAAGhE,SAAS,IAAI2L,WAAW,CAAC3L,SAAD,EAAYmJ,WAAZ,CAAzC;;EAEA,QAAInF,QAAJ,EAAc;EACZzE,MAAAA,GAAG,CAACV,IAAJ,CAASmF,QAAT;EACD;;EAED,WAAOzE,GAAP;EACD,GATe,EAUhB,EAVgB,CAAlB;EAaA,SAAOG,SAAS,CAACiG,OAAD,CAAT,GAAqByN,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;EACD;;EAEDL,KAAK,CAAChM,YAAN,GAAqBA,YAArB;EACAgM,KAAK,CAAChK,eAAN,GAAwBA,eAAxB;EACAgK,KAAK,CAAC1P,YAAN,GAAqBA,YAArB;AAEA,EAEO,IAAMgQ,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;EAAA,gCAAP,EAAO;EAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;EAAA,MADzBnM,QACyB,QADzBA,QACyB;;EACzBsE,EAAAA,gBAAgB,CAACtN,OAAjB,CAAyB,UAAC4F,QAAD,EAAc;EACrC,QAAIwP,UAAU,GAAG,KAAjB;;EAEA,QAAIF,2BAAJ,EAAiC;EAC/BE,MAAAA,UAAU,GAAG1T,kBAAkB,CAACwT,2BAAD,CAAlB,GACTtP,QAAQ,CAAChE,SAAT,KAAuBsT,2BADd,GAETtP,QAAQ,CAAC0G,MAAT,KAAqB4I,2BAAD,CAA0C5I,MAFlE;EAGD;;EAED,QAAI,CAAC8I,UAAL,EAAiB;EACf,UAAMC,gBAAgB,GAAGzP,QAAQ,CAAC3C,KAAT,CAAe+F,QAAxC;EAEApD,MAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAAC7F,QAAAA,QAAQ,EAARA;EAAD,OAAlB;EACApD,MAAAA,QAAQ,CAACmJ,IAAT;;EAEA,UAAI,CAACnJ,QAAQ,CAACxD,KAAT,CAAeqM,WAApB,EAAiC;EAC/B7I,QAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAAC7F,UAAAA,QAAQ,EAAEqM;EAAX,SAAlB;EACD;EACF;EACF,GAnBD;EAoBD,CAxBM;;ECrDP;EACA;EACA;;EACA,IAAMC,mBAAqE,qBACtEC,gBADsE;EAEzEC,EAAAA,MAFyE,wBAEzD;EAAA,QAARpT,KAAQ,QAARA,KAAQ;EACd,QAAMqT,aAAa,GAAG;EACpBnJ,MAAAA,MAAM,EAAE;EACNoJ,QAAAA,QAAQ,EAAEtT,KAAK,CAAC6P,OAAN,CAAc0D,QADlB;EAEN9R,QAAAA,IAAI,EAAE,GAFA;EAGNL,QAAAA,GAAG,EAAE,GAHC;EAINoS,QAAAA,MAAM,EAAE;EAJF,OADY;EAOpBxN,MAAAA,KAAK,EAAE;EACLsN,QAAAA,QAAQ,EAAE;EADL,OAPa;EAUpB9T,MAAAA,SAAS,EAAE;EAVS,KAAtB;EAaAX,IAAAA,MAAM,CAAC4U,MAAP,CAAczT,KAAK,CAACyS,QAAN,CAAevI,MAAf,CAAsB3P,KAApC,EAA2C8Y,aAAa,CAACnJ,MAAzD;EACAlK,IAAAA,KAAK,CAAC0T,MAAN,GAAeL,aAAf;;EAEA,QAAIrT,KAAK,CAACyS,QAAN,CAAezM,KAAnB,EAA0B;EACxBnH,MAAAA,MAAM,CAAC4U,MAAP,CAAczT,KAAK,CAACyS,QAAN,CAAezM,KAAf,CAAqBzL,KAAnC,EAA0C8Y,aAAa,CAACrN,KAAxD;EACD,KAnBa;EAsBd;;EACD;EAzBwE,EAA3E;;EA4BA,IAAM2N,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCpB,aAFuC,EAGpC;EAAA;;EAAA,MADHA,aACG;EADHA,IAAAA,aACG,GADa,EACb;EAAA;;EACH;EACA,EAAa;EACXxN,IAAAA,SAAS,CACP,CAACxI,KAAK,CAACC,OAAN,CAAcmX,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGErO,MAAM,CAACqO,cAAD,CAHR,EAIE9P,IAJF,CAIO,GAJP,CAFO,CAAT;EAQD;;EAED,MAAI+P,mBAAmB,GAAGD,cAA1B;EACA,MAAIE,UAAmC,GAAG,EAA1C;EACA,MAAIC,cAA8B,GAAG,EAArC;EACA,MAAI9H,aAAJ;EACA,MAAI+H,SAAS,GAAGxB,aAAa,CAACwB,SAA9B;EACA,MAAIC,yBAA4C,GAAG,EAAnD;EACA,MAAIC,aAAa,GAAG,KAApB;;EAEA,WAASC,iBAAT,GAAmC;EACjCJ,IAAAA,cAAc,GAAGF,mBAAmB,CACjC3G,GADc,CACV,UAAC1J,QAAD;EAAA,aACHvF,gBAAgB,CAACuF,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7E,QAAQ,CAAChE,SAA1C,CADb;EAAA,KADU,EAIdV,MAJc,CAIP,UAACC,GAAD,EAAMR,IAAN;EAAA,aAAeQ,GAAG,CAACb,MAAJ,CAAWK,IAAX,CAAf;EAAA,KAJO,EAI0B,EAJ1B,CAAjB;EAKD;;EAED,WAAS6V,aAAT,GAA+B;EAC7BN,IAAAA,UAAU,GAAGD,mBAAmB,CAAC3G,GAApB,CAAwB,UAAC1J,QAAD;EAAA,aAAcA,QAAQ,CAAChE,SAAvB;EAAA,KAAxB,CAAb;EACD;;EAED,WAAS6U,eAAT,CAAyBjI,SAAzB,EAAmD;EACjDyH,IAAAA,mBAAmB,CAACjW,OAApB,CAA4B,UAAC4F,QAAD,EAAc;EACxC,UAAI4I,SAAJ,EAAe;EACb5I,QAAAA,QAAQ,CAACqJ,MAAT;EACD,OAFD,MAEO;EACLrJ,QAAAA,QAAQ,CAACsJ,OAAT;EACD;EACF,KAND;EAOD;;EAED,WAASwH,iBAAT,CAA2BC,SAA3B,EAAmE;EACjE,WAAOV,mBAAmB,CAAC3G,GAApB,CAAwB,UAAC1J,QAAD,EAAc;EAC3C,UAAMgR,gBAAgB,GAAGhR,QAAQ,CAACiJ,QAAlC;;EAEAjJ,MAAAA,QAAQ,CAACiJ,QAAT,GAAoB,UAAC5L,KAAD,EAAiB;EACnC2T,QAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;;EAEA,YAAI2C,QAAQ,CAAChE,SAAT,KAAuByM,aAA3B,EAA0C;EACxCsI,UAAAA,SAAS,CAAC9H,QAAV,CAAmB5L,KAAnB;EACD;EACF,OAND;;EAQA,aAAO,YAAY;EACjB2C,QAAAA,QAAQ,CAACiJ,QAAT,GAAoB+H,gBAApB;EACD,OAFD;EAGD,KAdM,CAAP;EAeD,GA3DE;;;EA8DH,WAASC,eAAT,CACEF,SADF,EAEE9R,MAFF,EAGQ;EACN,QAAMnG,KAAK,GAAGyX,cAAc,CAAChX,OAAf,CAAuB0F,MAAvB,CAAd,CADM;;EAIN,QAAIA,MAAM,KAAKwJ,aAAf,EAA8B;EAC5B;EACD;;EAEDA,IAAAA,aAAa,GAAGxJ,MAAhB;EAEA,QAAMiS,aAA6B,GAAG,CAACV,SAAS,IAAI,EAAd,EACnC9V,MADmC,CAC5B,SAD4B,EAEnCY,MAFmC,CAE5B,UAACC,GAAD,EAAMwK,IAAN,EAAe;EACpBxK,MAAAA,GAAD,CAAawK,IAAb,IAAqBsK,mBAAmB,CAACvX,KAAD,CAAnB,CAA2BuE,KAA3B,CAAiC0I,IAAjC,CAArB;EACA,aAAOxK,GAAP;EACD,KALmC,EAKjC,EALiC,CAAtC;EAOAwV,IAAAA,SAAS,CAAC9H,QAAV,mBACKiI,aADL;EAEE7N,MAAAA,sBAAsB,EACpB,OAAO6N,aAAa,CAAC7N,sBAArB,KAAgD,UAAhD,GACI6N,aAAa,CAAC7N,sBADlB,GAEI;EAAA;;EAAA,oCAAkBiN,UAAU,CAACxX,KAAD,CAA5B,qBAAkB,kBAAmBiU,qBAAnB,EAAlB;EAAA;EALR;EAOD;;EAED8D,EAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,EAAAA,aAAa;EACbD,EAAAA,iBAAiB;EAEjB,MAAMvL,MAAc,GAAG;EACrBzL,IAAAA,EADqB,gBAChB;EACH,aAAO;EACLmK,QAAAA,SADK,uBACa;EAChB+M,UAAAA,eAAe,CAAC,IAAD,CAAf;EACD,SAHI;EAIL9M,QAAAA,QAJK,sBAIY;EACf0E,UAAAA,aAAa,GAAG,IAAhB;EACD,SANI;EAOLnE,QAAAA,cAPK,0BAOUtE,QAPV,EAO0B;EAC7B,cAAIA,QAAQ,CAAC3C,KAAT,CAAeqH,YAAf,IAA+B,CAACgM,aAApC,EAAmD;EACjDA,YAAAA,aAAa,GAAG,IAAhB;EACAjI,YAAAA,aAAa,GAAG,IAAhB;EACD;EACF,SAZI;EAaLvE,QAAAA,MAbK,kBAaElE,QAbF,EAakB;EACrB,cAAIA,QAAQ,CAAC3C,KAAT,CAAeqH,YAAf,IAA+B,CAACgM,aAApC,EAAmD;EACjDA,YAAAA,aAAa,GAAG,IAAhB;EACAO,YAAAA,eAAe,CAACjR,QAAD,EAAWsQ,UAAU,CAAC,CAAD,CAArB,CAAf;EACD;EACF,SAlBI;EAmBLlM,QAAAA,SAnBK,qBAmBKpE,QAnBL,EAmBejD,KAnBf,EAmB4B;EAC/BkU,UAAAA,eAAe,CAACjR,QAAD,EAAWjD,KAAK,CAAC0L,aAAjB,CAAf;EACD;EArBI,OAAP;EAuBD;EAzBoB,GAAvB;EA4BA,MAAMsI,SAAS,GAAGhC,KAAK,CAACtT,GAAG,EAAJ,oBAClBxB,gBAAgB,CAAC+U,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADE;EAErBzK,IAAAA,OAAO,GAAGa,MAAH,SAAe4J,aAAa,CAACzK,OAAd,IAAyB,EAAxC,CAFc;EAGrBM,IAAAA,aAAa,EAAE0L,cAHM;EAIrB/L,IAAAA,aAAa,oBACRwK,aAAa,CAACxK,aADN;EAEXmJ,MAAAA,SAAS,YACH,0BAAAqB,aAAa,CAACxK,aAAd,2CAA6BmJ,SAA7B,KAA0C,EADvC,GAEP+B,mBAFO;EAFE;EAJQ,KAAvB;EAaA,MAAMyB,YAAY,GAAGJ,SAAS,CAAC7H,IAA/B;;EAEA6H,EAAAA,SAAS,CAAC7H,IAAV,GAAiB,UAACjK,MAAD,EAAyD;EACxEkS,IAAAA,YAAY,GAD4D;EAIxE;;EACA,QAAI,CAAC1I,aAAD,IAAkBxJ,MAAM,IAAI,IAAhC,EAAsC;EACpC,aAAOgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAAC,CAAD,CAAtB,CAAtB;EACD,KAPuE;EAUxE;;;EACA,QAAI7H,aAAa,IAAIxJ,MAAM,IAAI,IAA/B,EAAqC;EACnC;EACD,KAbuE;;;EAgBxE,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aACEqR,UAAU,CAACrR,MAAD,CAAV,IAAsBgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAACrR,MAAD,CAAtB,CADvC;EAGD,KApBuE;;;EAuBxE,QAAIoR,mBAAmB,CAAC9W,OAApB,CAA4B0F,MAA5B,KAAmD,CAAvD,EAA0D;EACxD,UAAMmS,GAAG,GAAInS,MAAD,CAAqBjD,SAAjC;EACA,aAAOiV,eAAe,CAACF,SAAD,EAAYK,GAAZ,CAAtB;EACD,KA1BuE;;;EA6BxE,QAAId,UAAU,CAAC/W,OAAX,CAAmB0F,MAAnB,KAAkD,CAAtD,EAAyD;EACvD,aAAOgS,eAAe,CAACF,SAAD,EAAY9R,MAAZ,CAAtB;EACD;EACF,GAhCD;;EAkCA8R,EAAAA,SAAS,CAACM,QAAV,GAAqB,YAAY;EAC/B,QAAMC,KAAK,GAAGhB,UAAU,CAAC,CAAD,CAAxB;;EACA,QAAI,CAAC7H,aAAL,EAAoB;EAClB,aAAOsI,SAAS,CAAC7H,IAAV,CAAe,CAAf,CAAP;EACD;;EACD,QAAMpQ,KAAK,GAAGwX,UAAU,CAAC/W,OAAX,CAAmBkP,aAAnB,CAAd;EACAsI,IAAAA,SAAS,CAAC7H,IAAV,CAAeoH,UAAU,CAACxX,KAAK,GAAG,CAAT,CAAV,IAAyBwY,KAAxC;EACD,GAPD;;EASAP,EAAAA,SAAS,CAACQ,YAAV,GAAyB,YAAY;EACnC,QAAMC,IAAI,GAAGlB,UAAU,CAACA,UAAU,CAACpK,MAAX,GAAoB,CAArB,CAAvB;;EACA,QAAI,CAACuC,aAAL,EAAoB;EAClB,aAAOsI,SAAS,CAAC7H,IAAV,CAAesI,IAAf,CAAP;EACD;;EACD,QAAM1Y,KAAK,GAAGwX,UAAU,CAAC/W,OAAX,CAAmBkP,aAAnB,CAAd;EACA,QAAMxJ,MAAM,GAAGqR,UAAU,CAACxX,KAAK,GAAG,CAAT,CAAV,IAAyB0Y,IAAxC;EACAT,IAAAA,SAAS,CAAC7H,IAAV,CAAejK,MAAf;EACD,GARD;;EAUA,MAAM+R,gBAAgB,GAAGD,SAAS,CAAC9H,QAAnC;;EAEA8H,EAAAA,SAAS,CAAC9H,QAAV,GAAqB,UAAC5L,KAAD,EAAiB;EACpCmT,IAAAA,SAAS,GAAGnT,KAAK,CAACmT,SAAN,IAAmBA,SAA/B;EACAQ,IAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;EACD,GAHD;;EAKA0T,EAAAA,SAAS,CAACU,YAAV,GAAyB,UAACC,aAAD,EAAyB;EAChDb,IAAAA,eAAe,CAAC,IAAD,CAAf;EACAJ,IAAAA,yBAAyB,CAACrW,OAA1B,CAAkC,UAACT,EAAD;EAAA,aAAQA,EAAE,EAAV;EAAA,KAAlC;EAEA0W,IAAAA,mBAAmB,GAAGqB,aAAtB;EAEAb,IAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,IAAAA,aAAa;EACbD,IAAAA,iBAAiB;EACjBF,IAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;EAEAA,IAAAA,SAAS,CAAC9H,QAAV,CAAmB;EAACpE,MAAAA,aAAa,EAAE0L;EAAhB,KAAnB;EACD,GAZD;;EAcAE,EAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;EAEA,SAAOA,SAAP;EACD,CA1ND;;ECvCA,IAAMY,mBAAmB,GAAG;EAC1BC,EAAAA,SAAS,EAAE,YADe;EAE1BC,EAAAA,OAAO,EAAE,OAFiB;EAG1BC,EAAAA,KAAK,EAAE;EAHmB,CAA5B;EAMA;EACA;EACA;EACA;;EACA,SAASC,QAAT,CACEpQ,OADF,EAEEtE,KAFF,EAGyB;EACvB;EACA,EAAa;EACXmE,IAAAA,SAAS,CACP,EAAEnE,KAAK,IAAIA,KAAK,CAAC4B,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEqB,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;EAED,MAAIgI,SAA2B,GAAG,EAAlC;EACA,MAAI0J,mBAA+B,GAAG,EAAtC;EACA,MAAIC,QAAQ,GAAG,KAAf;EAEA,MAAOhT,MAAP,GAAiB5B,KAAjB,CAAO4B,MAAP;EAEA,MAAMiT,WAAW,GAAGjY,gBAAgB,CAACoD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;EACA,MAAM8U,WAAW,qBAAOD,WAAP;EAAoBtN,IAAAA,OAAO,EAAE,QAA7B;EAAuCD,IAAAA,KAAK,EAAE;EAA9C,IAAjB;EACA,MAAMyN,UAAU;EACdzN,IAAAA,KAAK,EAAE5B,YAAY,CAAC4B;EADN,KAEXuN,WAFW;EAGdxN,IAAAA,YAAY,EAAE;EAHA,IAAhB;EAMA,MAAM2N,WAAW,GAAGtD,KAAK,CAACpN,OAAD,EAAUwQ,WAAV,CAAzB;EACA,MAAMG,qBAAqB,GAAG7X,gBAAgB,CAAC4X,WAAD,CAA9C;;EAEA,WAASjO,SAAT,CAAmBrH,KAAnB,EAAuC;EACrC,QAAI,CAACA,KAAK,CAACkC,MAAP,IAAiBgT,QAArB,EAA+B;EAC7B;EACD;;EAED,QAAMM,UAAU,GAAIxV,KAAK,CAACkC,MAAP,CAA0BuT,OAA1B,CAAkCvT,MAAlC,CAAnB;;EAEA,QAAI,CAACsT,UAAL,EAAiB;EACf;EACD,KAToC;EAYrC;EACA;EACA;;;EACA,QAAM3N,OAAO,GACX2N,UAAU,CAAC9M,YAAX,CAAwB,oBAAxB,KACApI,KAAK,CAACuH,OADN,IAEA7B,YAAY,CAAC6B,OAHf,CAfqC;;EAqBrC,QAAI2N,UAAU,CAACxW,MAAf,EAAuB;EACrB;EACD;;EAED,QAAIgB,KAAK,CAAC3D,IAAN,KAAe,YAAf,IAA+B,OAAOgZ,UAAU,CAACzN,KAAlB,KAA4B,SAA/D,EAA0E;EACxE;EACD;;EAED,QACE5H,KAAK,CAAC3D,IAAN,KAAe,YAAf,IACAwL,OAAO,CAACrL,OAAR,CAAiBoY,mBAAD,CAA6B5U,KAAK,CAAC3D,IAAnC,CAAhB,IAA4D,CAF9D,EAGE;EACA;EACD;;EAED,QAAM4G,QAAQ,GAAG+O,KAAK,CAACwD,UAAD,EAAaH,UAAb,CAAtB;;EAEA,QAAIpS,QAAJ,EAAc;EACZgS,MAAAA,mBAAmB,GAAGA,mBAAmB,CAACtX,MAApB,CAA2BsF,QAA3B,CAAtB;EACD;EACF;;EAED,WAASkM,EAAT,CACEnF,IADF,EAEEoF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADuC,KACvC;EAAA;;EACNtF,IAAAA,IAAI,CAACrH,gBAAL,CAAsByM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACA/D,IAAAA,SAAS,CAACzN,IAAV,CAAe;EAACkM,MAAAA,IAAI,EAAJA,IAAD;EAAOoF,MAAAA,SAAS,EAATA,SAAP;EAAkBC,MAAAA,OAAO,EAAPA,OAAlB;EAA2BC,MAAAA,OAAO,EAAPA;EAA3B,KAAf;EACD;;EAED,WAASoG,iBAAT,CAA2BzS,QAA3B,EAAqD;EACnD,QAAOhE,SAAP,GAAoBgE,QAApB,CAAOhE,SAAP;EAEAkQ,IAAAA,EAAE,CAAClQ,SAAD,EAAY,YAAZ,EAA0BoI,SAA1B,EAAqCjM,aAArC,CAAF;EACA+T,IAAAA,EAAE,CAAClQ,SAAD,EAAY,WAAZ,EAAyBoI,SAAzB,CAAF;EACA8H,IAAAA,EAAE,CAAClQ,SAAD,EAAY,SAAZ,EAAuBoI,SAAvB,CAAF;EACA8H,IAAAA,EAAE,CAAClQ,SAAD,EAAY,OAAZ,EAAqBoI,SAArB,CAAF;EACD;;EAED,WAASsO,oBAAT,GAAsC;EACpCpK,IAAAA,SAAS,CAAClO,OAAV,CAAkB,gBAAyD;EAAA,UAAvD2M,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDoF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEtF,MAAAA,IAAI,CAAClH,mBAAL,CAAyBsM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGA/D,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAASqK,cAAT,CAAwB3S,QAAxB,EAAkD;EAChD,QAAM4S,eAAe,GAAG5S,QAAQ,CAACwJ,OAAjC;EACA,QAAMqJ,cAAc,GAAG7S,QAAQ,CAACqJ,MAAhC;EACA,QAAMyJ,eAAe,GAAG9S,QAAQ,CAACsJ,OAAjC;;EAEAtJ,IAAAA,QAAQ,CAACwJ,OAAT,GAAmB,UAACuJ,2BAAD,EAA8C;EAAA,UAA7CA,2BAA6C;EAA7CA,QAAAA,2BAA6C,GAAf,IAAe;EAAA;;EAC/D,UAAIA,2BAAJ,EAAiC;EAC/Bf,QAAAA,mBAAmB,CAAC5X,OAApB,CAA4B,UAAC4F,QAAD,EAAc;EACxCA,UAAAA,QAAQ,CAACwJ,OAAT;EACD,SAFD;EAGD;;EAEDwI,MAAAA,mBAAmB,GAAG,EAAtB;EAEAU,MAAAA,oBAAoB;EACpBE,MAAAA,eAAe;EAChB,KAXD;;EAaA5S,IAAAA,QAAQ,CAACqJ,MAAT,GAAkB,YAAY;EAC5BwJ,MAAAA,cAAc;EACdb,MAAAA,mBAAmB,CAAC5X,OAApB,CAA4B,UAAC4F,QAAD;EAAA,eAAcA,QAAQ,CAACqJ,MAAT,EAAd;EAAA,OAA5B;EACA4I,MAAAA,QAAQ,GAAG,KAAX;EACD,KAJD;;EAMAjS,IAAAA,QAAQ,CAACsJ,OAAT,GAAmB,YAAY;EAC7BwJ,MAAAA,eAAe;EACfd,MAAAA,mBAAmB,CAAC5X,OAApB,CAA4B,UAAC4F,QAAD;EAAA,eAAcA,QAAQ,CAACsJ,OAAT,EAAd;EAAA,OAA5B;EACA2I,MAAAA,QAAQ,GAAG,IAAX;EACD,KAJD;;EAMAQ,IAAAA,iBAAiB,CAACzS,QAAD,CAAjB;EACD;;EAEDsS,EAAAA,qBAAqB,CAAClY,OAAtB,CAA8BuY,cAA9B;EAEA,SAAON,WAAP;EACD;;ECrJD,IAAMpQ,WAAwB,GAAG;EAC/BoD,EAAAA,IAAI,EAAE,aADyB;EAE/BtM,EAAAA,YAAY,EAAE,KAFiB;EAG/BY,EAAAA,EAH+B,cAG5BqG,QAH4B,EAGlB;EAAA;;EACX;EACA,QAAI,2BAACA,QAAQ,CAAC3C,KAAT,CAAeoH,MAAhB,aAAC,sBAAuB8C,OAAxB,CAAJ,EAAqC;EACnC,MAAa;EACX/F,QAAAA,SAAS,CACPxB,QAAQ,CAAC3C,KAAT,CAAe4E,WADR,EAEP,gEAFO,CAAT;EAID;;EAED,aAAO,EAAP;EACD;;EAED,uBAAuBwE,WAAW,CAACzG,QAAQ,CAAC0G,MAAV,CAAlC;EAAA,QAAOhI,GAAP,gBAAOA,GAAP;EAAA,QAAY+D,OAAZ,gBAAYA,OAAZ;;EAEA,QAAMwE,QAAQ,GAAGjH,QAAQ,CAAC3C,KAAT,CAAe4E,WAAf,GACb+Q,qBAAqB,EADR,GAEb,IAFJ;EAIA,WAAO;EACLnP,MAAAA,QADK,sBACY;EACf,YAAIoD,QAAJ,EAAc;EACZvI,UAAAA,GAAG,CAACnH,YAAJ,CAAiB0P,QAAjB,EAA2BvI,GAAG,CAACiI,iBAA/B;EACAjI,UAAAA,GAAG,CAACvH,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;EACAuH,UAAAA,GAAG,CAAC3H,KAAJ,CAAUkc,QAAV,GAAqB,QAArB;EAEAjT,UAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAACzG,YAAAA,KAAK,EAAE,KAAR;EAAeD,YAAAA,SAAS,EAAE;EAA1B,WAAlB;EACD;EACF,OATI;EAUL0B,MAAAA,OAVK,qBAUW;EACd,YAAIgD,QAAJ,EAAc;EACZ,cAAO3K,kBAAP,GAA6BoC,GAAG,CAAC3H,KAAjC,CAAOuF,kBAAP;EACA,cAAM8G,QAAQ,GAAG8P,MAAM,CAAC5W,kBAAkB,CAACoE,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;EAKZ;EACA;;EACA+B,UAAAA,OAAO,CAAC1L,KAAR,CAAcoc,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAWjQ,QAAQ,GAAG,EAAtB,CAAnC;EAEA6D,UAAAA,QAAQ,CAAClQ,KAAT,CAAeuF,kBAAf,GAAoCA,kBAApC;EACAC,UAAAA,kBAAkB,CAAC,CAAC0K,QAAD,CAAD,EAAa,SAAb,CAAlB;EACD;EACF,OAvBI;EAwBL/C,MAAAA,MAxBK,oBAwBU;EACb,YAAI+C,QAAJ,EAAc;EACZA,UAAAA,QAAQ,CAAClQ,KAAT,CAAeuF,kBAAf,GAAoC,KAApC;EACD;EACF,OA5BI;EA6BL0H,MAAAA,MA7BK,oBA6BU;EACb,YAAIiD,QAAJ,EAAc;EACZ1K,UAAAA,kBAAkB,CAAC,CAAC0K,QAAD,CAAD,EAAa,QAAb,CAAlB;EACD;EACF;EAjCI,KAAP;EAmCD;EAzD8B,CAAjC;AA4DA;EAEA,SAAS+L,qBAAT,GAAiD;EAC/C,MAAM/L,QAAQ,GAAGxL,GAAG,EAApB;EACAwL,EAAAA,QAAQ,CAACV,SAAT,GAAqBvO,cAArB;EACAuE,EAAAA,kBAAkB,CAAC,CAAC0K,QAAD,CAAD,EAAa,QAAb,CAAlB;EACA,SAAOA,QAAP;EACD;;ECtED,IAAIqM,WAAW,GAAG;EAACtW,EAAAA,OAAO,EAAE,CAAV;EAAaC,EAAAA,OAAO,EAAE;EAAtB,CAAlB;EACA,IAAIsW,eAA2D,GAAG,EAAlE;;EAEA,SAASC,gBAAT,OAAgE;EAAA,MAArCxW,OAAqC,QAArCA,OAAqC;EAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;EAC9DqW,EAAAA,WAAW,GAAG;EAACtW,IAAAA,OAAO,EAAPA,OAAD;EAAUC,IAAAA,OAAO,EAAPA;EAAV,GAAd;EACD;;EAED,SAASwW,sBAAT,CAAgC5H,GAAhC,EAAqD;EACnDA,EAAAA,GAAG,CAACnM,gBAAJ,CAAqB,WAArB,EAAkC8T,gBAAlC;EACD;;EAED,SAASE,yBAAT,CAAmC7H,GAAnC,EAAwD;EACtDA,EAAAA,GAAG,CAAChM,mBAAJ,CAAwB,WAAxB,EAAqC2T,gBAArC;EACD;;EAED,IAAMtR,YAA0B,GAAG;EACjCmD,EAAAA,IAAI,EAAE,cAD2B;EAEjCtM,EAAAA,YAAY,EAAE,KAFmB;EAGjCY,EAAAA,EAHiC,cAG9BqG,QAH8B,EAGpB;EACX,QAAMhE,SAAS,GAAGgE,QAAQ,CAAChE,SAA3B;EACA,QAAM6P,GAAG,GAAGpP,gBAAgB,CAACuD,QAAQ,CAAC3C,KAAT,CAAewH,aAAf,IAAgC7I,SAAjC,CAA5B;EAEA,QAAI2X,gBAAgB,GAAG,KAAvB;EACA,QAAIC,aAAa,GAAG,KAApB;EACA,QAAIC,WAAW,GAAG,IAAlB;EACA,QAAI1M,SAAS,GAAGnH,QAAQ,CAAC3C,KAAzB;;EAEA,aAASyW,oBAAT,GAAyC;EACvC,aACE9T,QAAQ,CAAC3C,KAAT,CAAe6E,YAAf,KAAgC,SAAhC,IAA6ClC,QAAQ,CAACxD,KAAT,CAAe0D,SAD9D;EAGD;;EAED,aAAS6T,WAAT,GAA6B;EAC3BlI,MAAAA,GAAG,CAACnM,gBAAJ,CAAqB,WAArB,EAAkC8I,WAAlC;EACD;;EAED,aAASwL,cAAT,GAAgC;EAC9BnI,MAAAA,GAAG,CAAChM,mBAAJ,CAAwB,WAAxB,EAAqC2I,WAArC;EACD;;EAED,aAASyL,2BAAT,GAA6C;EAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;EACA3T,MAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAAC5F,QAAAA,sBAAsB,EAAE;EAAzB,OAAlB;EACAsQ,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAASnL,WAAT,CAAqBzL,KAArB,EAA8C;EAC5C;EACA;EACA,UAAMmX,qBAAqB,GAAGnX,KAAK,CAACkC,MAAN,GAC1BjD,SAAS,CAACkD,QAAV,CAAmBnC,KAAK,CAACkC,MAAzB,CAD0B,GAE1B,IAFJ;EAGA,UAAOiD,YAAP,GAAuBlC,QAAQ,CAAC3C,KAAhC,CAAO6E,YAAP;EACA,UAAOlF,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;EAAA,UAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;EAEA,UAAMkX,IAAI,GAAGnY,SAAS,CAAC+Q,qBAAV,EAAb;EACA,UAAMqH,SAAS,GAAGpX,OAAO,GAAGmX,IAAI,CAAClW,IAAjC;EACA,UAAMoW,SAAS,GAAGpX,OAAO,GAAGkX,IAAI,CAACvW,GAAjC;;EAEA,UAAIsW,qBAAqB,IAAI,CAAClU,QAAQ,CAAC3C,KAAT,CAAemG,WAA7C,EAA0D;EACxDxD,QAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAChB;EACA5F,UAAAA,sBAFgB,oCAES;EACvB,gBAAM8Q,IAAI,GAAGnY,SAAS,CAAC+Q,qBAAV,EAAb;EAEA,gBAAI7O,CAAC,GAAGlB,OAAR;EACA,gBAAIa,CAAC,GAAGZ,OAAR;;EAEA,gBAAIiF,YAAY,KAAK,SAArB,EAAgC;EAC9BhE,cAAAA,CAAC,GAAGiW,IAAI,CAAClW,IAAL,GAAYmW,SAAhB;EACAvW,cAAAA,CAAC,GAAGsW,IAAI,CAACvW,GAAL,GAAWyW,SAAf;EACD;;EAED,gBAAMzW,GAAG,GAAGsE,YAAY,KAAK,YAAjB,GAAgCiS,IAAI,CAACvW,GAArC,GAA2CC,CAAvD;EACA,gBAAMO,KAAK,GAAG8D,YAAY,KAAK,UAAjB,GAA8BiS,IAAI,CAAC/V,KAAnC,GAA2CF,CAAzD;EACA,gBAAMH,MAAM,GAAGmE,YAAY,KAAK,YAAjB,GAAgCiS,IAAI,CAACpW,MAArC,GAA8CF,CAA7D;EACA,gBAAMI,IAAI,GAAGiE,YAAY,KAAK,UAAjB,GAA8BiS,IAAI,CAAClW,IAAnC,GAA0CC,CAAvD;EAEA,mBAAO;EACLoW,cAAAA,KAAK,EAAElW,KAAK,GAAGH,IADV;EAELsW,cAAAA,MAAM,EAAExW,MAAM,GAAGH,GAFZ;EAGLA,cAAAA,GAAG,EAAHA,GAHK;EAILQ,cAAAA,KAAK,EAALA,KAJK;EAKLL,cAAAA,MAAM,EAANA,MALK;EAMLE,cAAAA,IAAI,EAAJA;EANK,aAAP;EAQD;EA1Be,SAAlB;EA4BD;EACF;;EAED,aAASuW,MAAT,GAAwB;EACtB,UAAIxU,QAAQ,CAAC3C,KAAT,CAAe6E,YAAnB,EAAiC;EAC/BqR,QAAAA,eAAe,CAAC1Y,IAAhB,CAAqB;EAACmF,UAAAA,QAAQ,EAARA,QAAD;EAAW6L,UAAAA,GAAG,EAAHA;EAAX,SAArB;EACA4H,QAAAA,sBAAsB,CAAC5H,GAAD,CAAtB;EACD;EACF;;EAED,aAASrC,OAAT,GAAyB;EACvB+J,MAAAA,eAAe,GAAGA,eAAe,CAAChZ,MAAhB,CAChB,UAACka,IAAD;EAAA,eAAUA,IAAI,CAACzU,QAAL,KAAkBA,QAA5B;EAAA,OADgB,CAAlB;;EAIA,UAAIuT,eAAe,CAAChZ,MAAhB,CAAuB,UAACka,IAAD;EAAA,eAAUA,IAAI,CAAC5I,GAAL,KAAaA,GAAvB;EAAA,OAAvB,EAAmD3F,MAAnD,KAA8D,CAAlE,EAAqE;EACnEwN,QAAAA,yBAAyB,CAAC7H,GAAD,CAAzB;EACD;EACF;;EAED,WAAO;EACLhI,MAAAA,QAAQ,EAAE2Q,MADL;EAEL1Q,MAAAA,SAAS,EAAE0F,OAFN;EAGL5F,MAAAA,cAHK,4BAGkB;EACrBuD,QAAAA,SAAS,GAAGnH,QAAQ,CAAC3C,KAArB;EACD,OALI;EAMLsG,MAAAA,aANK,yBAMS+Q,CANT,SAMkC;EAAA,YAArBxS,YAAqB,SAArBA,YAAqB;;EACrC,YAAIyR,gBAAJ,EAAsB;EACpB;EACD;;EAED,YACEzR,YAAY,KAAK1G,SAAjB,IACA2L,SAAS,CAACjF,YAAV,KAA2BA,YAF7B,EAGE;EACAsH,UAAAA,OAAO;;EAEP,cAAItH,YAAJ,EAAkB;EAChBsS,YAAAA,MAAM;;EAEN,gBACExU,QAAQ,CAACxD,KAAT,CAAesM,SAAf,IACA,CAAC8K,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;EACAC,cAAAA,WAAW;EACZ;EACF,WAVD,MAUO;EACLC,YAAAA,cAAc;EACdC,YAAAA,2BAA2B;EAC5B;EACF;EACF,OAhCI;EAiCLhQ,MAAAA,OAjCK,qBAiCW;EACd,YAAIjE,QAAQ,CAAC3C,KAAT,CAAe6E,YAAf,IAA+B,CAAC0R,aAApC,EAAmD;EACjD,cAAIC,WAAJ,EAAiB;EACfrL,YAAAA,WAAW,CAAC8K,WAAD,CAAX;EACAO,YAAAA,WAAW,GAAG,KAAd;EACD;;EAED,cAAI,CAACC,oBAAoB,EAAzB,EAA6B;EAC3BC,YAAAA,WAAW;EACZ;EACF;EACF,OA5CI;EA6CL3P,MAAAA,SA7CK,qBA6CKsQ,CA7CL,EA6CQ3X,KA7CR,EA6CqB;EACxB,YAAIlB,YAAY,CAACkB,KAAD,CAAhB,EAAyB;EACvBuW,UAAAA,WAAW,GAAG;EAACtW,YAAAA,OAAO,EAAED,KAAK,CAACC,OAAhB;EAAyBC,YAAAA,OAAO,EAAEF,KAAK,CAACE;EAAxC,WAAd;EACD;;EACD2W,QAAAA,aAAa,GAAG7W,KAAK,CAAC3D,IAAN,KAAe,OAA/B;EACD,OAlDI;EAmDL2K,MAAAA,QAnDK,sBAmDY;EACf,YAAI/D,QAAQ,CAAC3C,KAAT,CAAe6E,YAAnB,EAAiC;EAC/B+R,UAAAA,2BAA2B;EAC3BD,UAAAA,cAAc;EACdH,UAAAA,WAAW,GAAG,IAAd;EACD;EACF;EAzDI,KAAP;EA2DD;EAzJgC,CAAnC;;ECbA,SAASc,QAAT,CAAkBtX,KAAlB,EAAgCuX,QAAhC,EAA8E;EAAA;;EAC5E,SAAO;EACLpQ,IAAAA,aAAa,oBACRnH,KAAK,CAACmH,aADE;EAEXmJ,MAAAA,SAAS,YACJ,CAAC,yBAAAtQ,KAAK,CAACmH,aAAN,0CAAqBmJ,SAArB,KAAkC,EAAnC,EAAuCpT,MAAvC,CACD;EAAA,YAAE8K,IAAF,QAAEA,IAAF;EAAA,eAAYA,IAAI,KAAKuP,QAAQ,CAACvP,IAA9B;EAAA,OADC,CADI,GAIPuP,QAJO;EAFE;EADR,GAAP;EAWD;;EAED,IAAMzS,iBAAoC,GAAG;EAC3CkD,EAAAA,IAAI,EAAE,mBADqC;EAE3CtM,EAAAA,YAAY,EAAE,KAF6B;EAG3CY,EAAAA,EAH2C,cAGxCqG,QAHwC,EAG9B;EACX,QAAOhE,SAAP,GAAoBgE,QAApB,CAAOhE,SAAP;;EAEA,aAAS4M,SAAT,GAA8B;EAC5B,aAAO,CAAC,CAAC5I,QAAQ,CAAC3C,KAAT,CAAe8E,iBAAxB;EACD;;EAED,QAAIlH,SAAJ;EACA,QAAI4Z,eAAe,GAAG,CAAC,CAAvB;EACA,QAAIlB,gBAAgB,GAAG,KAAvB;EACA,QAAImB,eAA8B,GAAG,EAArC;EAEA,QAAMF,QAGL,GAAG;EACFvP,MAAAA,IAAI,EAAE,wBADJ;EAEFkI,MAAAA,OAAO,EAAE,IAFP;EAGFC,MAAAA,KAAK,EAAE,YAHL;EAIF7T,MAAAA,EAJE,qBAIU;EAAA,YAAR6C,KAAQ,SAARA,KAAQ;;EACV,YAAIoM,SAAS,EAAb,EAAiB;EACf,cAAIkM,eAAe,CAACvb,OAAhB,CAAwBiD,KAAK,CAACvB,SAA9B,MAA6C,CAAC,CAAlD,EAAqD;EACnD6Z,YAAAA,eAAe,GAAG,EAAlB;EACD;;EAED,cACE7Z,SAAS,KAAKuB,KAAK,CAACvB,SAApB,IACA6Z,eAAe,CAACvb,OAAhB,CAAwBiD,KAAK,CAACvB,SAA9B,MAA6C,CAAC,CAFhD,EAGE;EACA6Z,YAAAA,eAAe,CAACja,IAAhB,CAAqB2B,KAAK,CAACvB,SAA3B;EACA+E,YAAAA,QAAQ,CAACiJ,QAAT,CAAkB;EAChB;EACA5F,cAAAA,sBAAsB,EAAE;EAAA,uBACtBA,uBAAsB,CAAC7G,KAAK,CAACvB,SAAP,CADA;EAAA;EAFR,aAAlB;EAKD;;EAEDA,UAAAA,SAAS,GAAGuB,KAAK,CAACvB,SAAlB;EACD;EACF;EAxBC,KAHJ;;EA8BA,aAASoI,uBAAT,CAAgCpI,SAAhC,EAAwE;EACtE,aAAO8Z,2BAA2B,CAChC/Z,gBAAgB,CAACC,SAAD,CADgB,EAEhCe,SAAS,CAAC+Q,qBAAV,EAFgC,EAGhC7R,SAAS,CAACc,SAAS,CAACgZ,cAAV,EAAD,CAHuB,EAIhCH,eAJgC,CAAlC;EAMD;;EAED,aAASI,gBAAT,CAA0BjQ,YAA1B,EAA8D;EAC5D2O,MAAAA,gBAAgB,GAAG,IAAnB;EACA3T,MAAAA,QAAQ,CAACiJ,QAAT,CAAkBjE,YAAlB;EACA2O,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAASuB,WAAT,GAA6B;EAC3B,UAAI,CAACvB,gBAAL,EAAuB;EACrBsB,QAAAA,gBAAgB,CAACN,QAAQ,CAAC3U,QAAQ,CAAC3C,KAAV,EAAiBuX,QAAjB,CAAT,CAAhB;EACD;EACF;;EAED,WAAO;EACL/Q,MAAAA,QAAQ,EAAEqR,WADL;EAELvR,MAAAA,aAAa,EAAEuR,WAFV;EAGL9Q,MAAAA,SAHK,qBAGKsQ,CAHL,EAGQ3X,KAHR,EAGqB;EACxB,YAAIlB,YAAY,CAACkB,KAAD,CAAhB,EAAyB;EACvB,cAAMoY,KAAK,GAAGja,SAAS,CAAC8E,QAAQ,CAAChE,SAAT,CAAmBgZ,cAAnB,EAAD,CAAvB;EACA,cAAMI,UAAU,GAAGD,KAAK,CAACrO,IAAN,CACjB,UAACqN,IAAD;EAAA,mBACEA,IAAI,CAAClW,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACAmX,IAAI,CAAC/V,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEAmX,IAAI,CAACvW,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGAkX,IAAI,CAACpW,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;EAAA,WADiB,CAAnB;EAOA,cAAMnE,KAAK,GAAGqc,KAAK,CAAC5b,OAAN,CAAc6b,UAAd,CAAd;EACAP,UAAAA,eAAe,GAAG/b,KAAK,GAAG,CAAC,CAAT,GAAaA,KAAb,GAAqB+b,eAAvC;EACD;EACF,OAhBI;EAiBL9Q,MAAAA,QAjBK,sBAiBY;EACf8Q,QAAAA,eAAe,GAAG,CAAC,CAAnB;EACD;EAnBI,KAAP;EAqBD;EAvF0C,CAA7C;AA0FA,EAEO,SAASE,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILV,eAJK,EAYL;EACA;EACA,MAAIU,WAAW,CAACrP,MAAZ,GAAqB,CAArB,IAA0BmP,oBAAoB,KAAK,IAAvD,EAA6D;EAC3D,WAAOC,YAAP;EACD,GAJD;;;EAOA,MACEC,WAAW,CAACrP,MAAZ,KAAuB,CAAvB,IACA2O,eAAe,IAAI,CADnB,IAEAU,WAAW,CAAC,CAAD,CAAX,CAAetX,IAAf,GAAsBsX,WAAW,CAAC,CAAD,CAAX,CAAenX,KAHvC,EAIE;EACA,WAAOmX,WAAW,CAACV,eAAD,CAAX,IAAgCS,YAAvC;EACD;;EAED,UAAQD,oBAAR;EACE,SAAK,KAAL;EACA,SAAK,QAAL;EAAe;EACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;EACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAACrP,MAAZ,GAAqB,CAAtB,CAA5B;EACA,YAAMwP,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;EAEA,YAAMzX,GAAG,GAAG4X,SAAS,CAAC5X,GAAtB;EACA,YAAMG,MAAM,GAAG0X,QAAQ,CAAC1X,MAAxB;EACA,YAAME,IAAI,GAAGyX,KAAK,GAAGF,SAAS,CAACvX,IAAb,GAAoBwX,QAAQ,CAACxX,IAA/C;EACA,YAAMG,KAAK,GAAGsX,KAAK,GAAGF,SAAS,CAACpX,KAAb,GAAqBqX,QAAQ,CAACrX,KAAjD;EACA,YAAMkW,KAAK,GAAGlW,KAAK,GAAGH,IAAtB;EACA,YAAMsW,MAAM,GAAGxW,MAAM,GAAGH,GAAxB;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,GAAD;EAAMG,UAAAA,MAAM,EAANA,MAAN;EAAcE,UAAAA,IAAI,EAAJA,IAAd;EAAoBG,UAAAA,KAAK,EAALA,KAApB;EAA2BkW,UAAAA,KAAK,EAALA,KAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD,SAAK,MAAL;EACA,SAAK,OAAL;EAAc;EACZ,YAAMoB,OAAO,GAAGvC,IAAI,CAACwC,GAAL,OAAAxC,IAAI,EAAQmC,WAAW,CAAC7L,GAAZ,CAAgB,UAACyL,KAAD;EAAA,iBAAWA,KAAK,CAAClX,IAAjB;EAAA,SAAhB,CAAR,CAApB;EACA,YAAM4X,QAAQ,GAAGzC,IAAI,CAAC0C,GAAL,OAAA1C,IAAI,EAAQmC,WAAW,CAAC7L,GAAZ,CAAgB,UAACyL,KAAD;EAAA,iBAAWA,KAAK,CAAC/W,KAAjB;EAAA,SAAhB,CAAR,CAArB;EACA,YAAM2X,YAAY,GAAGR,WAAW,CAAChb,MAAZ,CAAmB,UAAC4Z,IAAD;EAAA,iBACtCkB,oBAAoB,KAAK,MAAzB,GACIlB,IAAI,CAAClW,IAAL,KAAc0X,OADlB,GAEIxB,IAAI,CAAC/V,KAAL,KAAeyX,QAHmB;EAAA,SAAnB,CAArB;EAMA,YAAMjY,IAAG,GAAGmY,YAAY,CAAC,CAAD,CAAZ,CAAgBnY,GAA5B;EACA,YAAMG,OAAM,GAAGgY,YAAY,CAACA,YAAY,CAAC7P,MAAb,GAAsB,CAAvB,CAAZ,CAAsCnI,MAArD;EACA,YAAME,KAAI,GAAG0X,OAAb;EACA,YAAMvX,MAAK,GAAGyX,QAAd;;EACA,YAAMvB,MAAK,GAAGlW,MAAK,GAAGH,KAAtB;;EACA,YAAMsW,OAAM,GAAGxW,OAAM,GAAGH,IAAxB;;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,IAAD;EAAMG,UAAAA,MAAM,EAANA,OAAN;EAAcE,UAAAA,IAAI,EAAJA,KAAd;EAAoBG,UAAAA,KAAK,EAALA,MAApB;EAA2BkW,UAAAA,KAAK,EAALA,MAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD;EAAS;EACP,eAAOe,YAAP;EACD;EArCH;EAuCD;;EC9KD,IAAMlT,MAAc,GAAG;EACrBiD,EAAAA,IAAI,EAAE,QADe;EAErBtM,EAAAA,YAAY,EAAE,KAFO;EAGrBY,EAAAA,EAHqB,cAGlBqG,QAHkB,EAGR;EACX,QAAOhE,SAAP,GAA4BgE,QAA5B,CAAOhE,SAAP;EAAA,QAAkB0K,MAAlB,GAA4B1G,QAA5B,CAAkB0G,MAAlB;;EAEA,aAASsP,YAAT,GAA2D;EACzD,aAAOhW,QAAQ,CAAC2I,cAAT,GACH3I,QAAQ,CAAC2I,cAAT,CAAwBnM,KAAxB,CAA8ByS,QAA9B,CAAuCjT,SADpC,GAEHA,SAFJ;EAGD;;EAED,aAASia,WAAT,CAAqBpd,KAArB,EAA6D;EAC3D,aAAOmH,QAAQ,CAAC3C,KAAT,CAAe+E,MAAf,KAA0B,IAA1B,IAAkCpC,QAAQ,CAAC3C,KAAT,CAAe+E,MAAf,KAA0BvJ,KAAnE;EACD;;EAED,QAAIqd,WAA8B,GAAG,IAArC;EACA,QAAIC,WAA8B,GAAG,IAArC;;EAEA,aAASC,cAAT,GAAgC;EAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGjJ,qBAAf,EADmB,GAEnB,IAFJ;EAGA,UAAMuJ,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnBvP,MAAM,CAACqG,qBAAP,EADmB,GAEnB,IAFJ;;EAIA,UACGsJ,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;EACA,YAAItW,QAAQ,CAAC2I,cAAb,EAA6B;EAC3B3I,UAAAA,QAAQ,CAAC2I,cAAT,CAAwB6N,MAAxB;EACD;EACF;;EAEDN,MAAAA,WAAW,GAAGG,cAAd;EACAF,MAAAA,WAAW,GAAGG,cAAd;;EAEA,UAAItW,QAAQ,CAACxD,KAAT,CAAesM,SAAnB,EAA8B;EAC5BqF,QAAAA,qBAAqB,CAACiI,cAAD,CAArB;EACD;EACF;;EAED,WAAO;EACLnS,MAAAA,OADK,qBACW;EACd,YAAIjE,QAAQ,CAAC3C,KAAT,CAAe+E,MAAnB,EAA2B;EACzBgU,UAAAA,cAAc;EACf;EACF;EALI,KAAP;EAOD;EAnDoB,CAAvB;AAsDA;EAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;EACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;EAClB,WACED,KAAK,CAAC7Y,GAAN,KAAc8Y,KAAK,CAAC9Y,GAApB,IACA6Y,KAAK,CAACrY,KAAN,KAAgBsY,KAAK,CAACtY,KADtB,IAEAqY,KAAK,CAAC1Y,MAAN,KAAiB2Y,KAAK,CAAC3Y,MAFvB,IAGA0Y,KAAK,CAACxY,IAAN,KAAeyY,KAAK,CAACzY,IAJvB;EAMD;;EAED,SAAO,IAAP;EACD;;EC5DD,IAAIxG,SAAJ,EAAe;EACbZ,EAAAA,SAAS,CAACC,GAAD,CAAT;EACD;;EAEDiY,KAAK,CAAChK,eAAN,CAAsB;EACpBR,EAAAA,OAAO,EAAE,CAACtC,WAAD,EAAcC,YAAd,EAA4BC,iBAA5B,EAA+CC,MAA/C,CADW;EAEpBqC,EAAAA,MAAM,EAANA;EAFoB,CAAtB;EAKAsK,KAAK,CAACoB,eAAN,GAAwBA,eAAxB;EACApB,KAAK,CAACgD,QAAN,GAAiBA,QAAjB;EACAhD,KAAK,CAACM,OAAN,GAAgBA,OAAhB;EACAN,KAAK,CAAC4H,UAAN,GAAmB9e,WAAnB;;;;;;;;"}