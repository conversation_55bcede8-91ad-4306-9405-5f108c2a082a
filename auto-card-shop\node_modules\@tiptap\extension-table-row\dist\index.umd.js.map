{"version": 3, "file": "index.umd.js", "sources": ["../src/table-row.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableRowOptions {\n  /**\n   * The HTML attributes for a table row node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table rows.\n * @see https://www.tiptap.dev/api/nodes/table-row\n */\nexport const TableRow = Node.create<TableRowOptions>({\n  name: 'tableRow',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: '(tableCell | tableHeader)*',\n\n  tableRole: 'row',\n\n  parseHTML() {\n    return [\n      { tag: 'tr' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['tr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;EAWA;;;EAGG;AACU,QAAA,QAAQ,GAAGA,SAAI,CAAC,MAAM,CAAkB;EACnD,IAAA,IAAI,EAAE,UAAU;MAEhB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,OAAO,EAAE,4BAA4B;EAErC,IAAA,SAAS,EAAE,KAAK;MAEhB,SAAS,GAAA;UACP,OAAO;cACL,EAAE,GAAG,EAAE,IAAI,EAAE;WACd;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC/E;EACF,CAAA;;;;;;;;;;;"}