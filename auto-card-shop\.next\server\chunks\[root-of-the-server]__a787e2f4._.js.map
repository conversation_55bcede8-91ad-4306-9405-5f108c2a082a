{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport CredentialsProvider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/orders/%5BorderId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\n// 更新订单状态（仅管理员）\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ orderId: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n\n    if (!session || session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: '权限不足' },\n        { status: 403 }\n      )\n    }\n\n    const { orderId } = await params\n    const { status } = await request.json()\n\n    if (!status || !['PENDING', 'PAID', 'DELIVERED', 'CANCELLED', 'REFUNDED'].includes(status)) {\n      return NextResponse.json(\n        { error: '无效的订单状态' },\n        { status: 400 }\n      )\n    }\n\n    const order = await prisma.order.update({\n      where: { id: orderId },\n      data: { \n        status,\n        updatedAt: new Date()\n      },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    // 如果状态改为已交付，需要分配卡密\n    if (status === 'DELIVERED' && order.status !== 'DELIVERED') {\n      for (const orderItem of order.orderItems) {\n        // 获取可用的卡密\n        const availableCards = await prisma.card.findMany({\n          where: {\n            productId: orderItem.productId,\n            status: 'AVAILABLE'\n          },\n          take: orderItem.quantity,\n          orderBy: {\n            createdAt: 'asc'\n          }\n        })\n\n        if (availableCards.length >= orderItem.quantity) {\n          // 标记卡密为已售出\n          await prisma.card.updateMany({\n            where: {\n              id: {\n                in: availableCards.map(card => card.id)\n              }\n            },\n            data: {\n              status: 'SOLD',\n              orderId: order.id,\n              usedAt: new Date()\n            }\n          })\n\n          // 更新商品库存\n          await prisma.product.update({\n            where: { id: orderItem.productId },\n            data: {\n              stockCount: {\n                decrement: orderItem.quantity\n              }\n            }\n          })\n        }\n      }\n    }\n\n    return NextResponse.json(order)\n  } catch (error) {\n    console.error('更新订单状态错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAA4C;IAEpD,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAO,GAChB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAErC,IAAI,CAAC,UAAU,CAAC;YAAC;YAAW;YAAQ;YAAa;YAAa;SAAW,CAAC,QAAQ,CAAC,SAAS;YAC1F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,IAAI;YAAQ;YACrB,MAAM;gBACJ;gBACA,WAAW,IAAI;YACjB;YACA,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,mBAAmB;QACnB,IAAI,WAAW,eAAe,MAAM,MAAM,KAAK,aAAa;YAC1D,KAAK,MAAM,aAAa,MAAM,UAAU,CAAE;gBACxC,UAAU;gBACV,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAChD,OAAO;wBACL,WAAW,UAAU,SAAS;wBAC9B,QAAQ;oBACV;oBACA,MAAM,UAAU,QAAQ;oBACxB,SAAS;wBACP,WAAW;oBACb;gBACF;gBAEA,IAAI,eAAe,MAAM,IAAI,UAAU,QAAQ,EAAE;oBAC/C,WAAW;oBACX,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBAC3B,OAAO;4BACL,IAAI;gCACF,IAAI,eAAe,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;4BACxC;wBACF;wBACA,MAAM;4BACJ,QAAQ;4BACR,SAAS,MAAM,EAAE;4BACjB,QAAQ,IAAI;wBACd;oBACF;oBAEA,SAAS;oBACT,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC1B,OAAO;4BAAE,IAAI,UAAU,SAAS;wBAAC;wBACjC,MAAM;4BACJ,YAAY;gCACV,WAAW,UAAU,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}