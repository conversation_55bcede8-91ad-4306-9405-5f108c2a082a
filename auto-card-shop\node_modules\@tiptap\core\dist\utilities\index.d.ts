export * from './callOrReturn.js';
export * from './canInsertNode.js';
export * from './createStyleTag.js';
export * from './deleteProps.js';
export * from './elementFromString.js';
export * from './escapeForRegEx.js';
export * from './findDuplicates.js';
export * from './fromString.js';
export * from './isEmptyObject.js';
export * from './isFunction.js';
export * from './isiOS.js';
export * from './isMacOS.js';
export * from './isNumber.js';
export * from './isPlainObject.js';
export * from './isRegExp.js';
export * from './isString.js';
export * from './mergeAttributes.js';
export * from './mergeDeep.js';
export * from './minMax.js';
export * from './objectIncludes.js';
export * from './removeDuplicates.js';
//# sourceMappingURL=index.d.ts.map