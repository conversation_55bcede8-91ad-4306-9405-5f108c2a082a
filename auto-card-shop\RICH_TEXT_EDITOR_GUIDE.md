# 富文本编辑器使用指南

自动发卡网站的管理后台现在支持两种编辑模式：**富文本编辑器**和**Markdown编辑器**，让您可以更方便地创建丰富的商品描述。

## 功能特色

### 🎨 双编辑模式
- **富文本编辑器**：所见即所得的可视化编辑体验
- **Markdown编辑器**：支持Markdown语法的文本编辑
- **一键切换**：两种模式之间可以无缝切换，内容自动转换

### ✨ 富文本编辑器功能

#### 文本格式
- **标题**：支持H1、H2、H3三级标题
- **粗体**：加粗重要文字
- **斜体**：强调特定内容
- **行内代码**：标记代码片段

#### 列表和结构
- **无序列表**：创建项目符号列表
- **有序列表**：创建编号列表
- **引用块**：突出显示重要信息
- **分隔线**：分割内容区块

#### 高级功能
- **链接插入**：添加外部链接
- **图片插入**：插入图片（支持URL）
- **表格创建**：创建和编辑表格
- **撤销/重做**：支持操作历史

## 使用方法

### 1. 进入编辑模式

1. 登录管理后台
2. 进入"商品管理"页面
3. 点击"添加商品"或编辑现有商品
4. 在商品描述区域选择编辑模式

### 2. 富文本编辑器操作

#### 基本文本格式
- 选中文字后点击工具栏按钮应用格式
- 或使用快捷键：
  - `Ctrl+B`：粗体
  - `Ctrl+I`：斜体
  - `Ctrl+Z`：撤销
  - `Ctrl+Y`：重做

#### 插入链接
1. 选中要添加链接的文字
2. 点击链接按钮（🔗）
3. 在弹出框中输入URL地址
4. 确认插入

#### 插入图片
1. 点击图片按钮（🖼️）
2. 在弹出框中输入图片URL
3. 确认插入

#### 创建表格
1. 点击表格按钮（📊）
2. 系统会自动插入3x3的表格
3. 点击表格单元格进行编辑
4. 可以添加或删除行列

### 3. 模式切换

#### 从富文本切换到Markdown
- 点击"Markdown"按钮
- 系统自动将HTML内容转换为Markdown格式
- 可以继续使用Markdown语法编辑

#### 从Markdown切换到富文本
- 点击"富文本"按钮
- 系统自动将Markdown内容转换为HTML格式
- 可以继续使用可视化编辑器

### 4. 预览功能

在Markdown模式下：
- 点击"预览"按钮查看渲染效果
- 点击"编辑"按钮返回编辑模式

## 最佳实践

### 1. 内容结构
```
# 商品主标题

## 产品特色
- 特色1
- 特色2
- 特色3

## 使用方法
1. 步骤1
2. 步骤2
3. 步骤3

## 注意事项
> 重要提醒内容

## 技术规格
| 项目 | 规格 |
|------|------|
| 平台 | Steam |
| 地区 | 全球 |
```

### 2. 图片使用
- 使用高质量的商品图片
- 确保图片URL可访问
- 建议图片宽度不超过800px
- 使用描述性的图片

### 3. 链接添加
- 链接到相关的官方页面
- 确保链接有效且安全
- 为链接添加描述性文字

### 4. 表格设计
- 保持表格简洁明了
- 使用表头说明列内容
- 避免过于复杂的表格结构

## 技术说明

### 支持的HTML标签
富文本编辑器生成的HTML包含以下标签：
- 标题：`<h1>`, `<h2>`, `<h3>`
- 文本：`<p>`, `<strong>`, `<em>`, `<code>`
- 列表：`<ul>`, `<ol>`, `<li>`
- 其他：`<blockquote>`, `<a>`, `<img>`, `<hr>`
- 表格：`<table>`, `<tr>`, `<td>`, `<th>`

### 内容转换
- HTML ↔ Markdown 自动转换
- 保持格式完整性
- 支持复杂内容结构

### 存储格式
- 富文本模式：存储为HTML格式
- Markdown模式：存储为Markdown格式
- 前端显示：统一渲染为格式化内容

## 注意事项

1. **内容长度**：建议商品描述控制在合理长度，避免过长影响用户体验
2. **图片链接**：确保图片URL稳定可访问，避免使用临时链接
3. **格式一致性**：在同一网站内保持描述格式的一致性
4. **移动端适配**：内容会自动适配移动设备显示
5. **SEO友好**：合理使用标题结构有助于搜索引擎优化

## 故障排除

### 常见问题

**Q: 切换模式后格式丢失？**
A: 系统会自动转换格式，但复杂格式可能有细微差异，建议在一种模式下完成编辑。

**Q: 图片无法显示？**
A: 检查图片URL是否正确，确保图片服务器允许外部访问。

**Q: 表格显示异常？**
A: 确保表格内容完整，避免空单元格或格式错误。

**Q: 链接无法点击？**
A: 在编辑器中链接不可点击是正常的，保存后在前端页面可以正常使用。

如有其他问题，请联系技术支持团队。
