'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Footer } from '@/components/ui/footer'
import { formatPrice } from '@/lib/utils'
import { ShoppingCart, User, LogIn, Package, Star, TrendingUp } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

interface Category {
  id: string
  name: string
  slug: string
  _count: {
    products: number
  }
}

export default function Home() {
  const { data: session } = useSession()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCategories()
    fetchProducts()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const fetchProducts = async (categoryId?: string) => {
    try {
      const url = categoryId
        ? `/api/products?categoryId=${categoryId}`
        : '/api/products'
      const response = await fetch(url)
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('获取商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setLoading(true)
    fetchProducts(categoryId || undefined)
  }

  const handleBuyNow = (productId: string) => {
    router.push(`/checkout?productId=${productId}&quantity=1`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold gradient-text hover:scale-105 transition-transform">
                ✨ 自动发卡网站
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              {session ? (
                <>
                  <Link href="/orders" className="text-gray-700 hover:text-primary transition-colors font-medium">
                    我的订单
                  </Link>
                  {session.user.role === 'ADMIN' && (
                    <>
                      <Link href="/admin">
                        <Badge variant="info" className="hover:scale-105 transition-transform cursor-pointer">
                          管理后台
                        </Badge>
                      </Link>
                      <Link href="/test-purchase">
                        <Badge variant="success" className="hover:scale-105 transition-transform cursor-pointer">
                          测试购买
                        </Badge>
                      </Link>
                      <Link href="/stripe-test">
                        <Badge variant="gradient" className="hover:scale-105 transition-transform cursor-pointer">
                          Stripe测试
                        </Badge>
                      </Link>
                      <Link href="/debug-payment">
                        <Badge variant="warning" className="hover:scale-105 transition-transform cursor-pointer">
                          支付调试
                        </Badge>
                      </Link>
                    </>
                  )}
                  <div className="flex items-center space-x-2 bg-white/50 rounded-full px-3 py-1">
                    <User className="w-4 h-4 text-primary" />
                    <span className="text-sm font-medium text-gray-700">{session.user.username}</span>
                  </div>
                </>
              ) : (
                <Link href="/auth/signin">
                  <Button variant="gradient" size="sm" className="font-medium">
                    <LogIn className="w-4 h-4 mr-2" />
                    登录
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 欢迎横幅 */}
        <div className="mb-12 text-center relative">
          {/* 装饰性背景元素 */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute top-10 left-1/4 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-bounce-gentle" />
            <div className="absolute top-20 right-1/4 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-bounce-gentle" style={{animationDelay: '1s'}} />
            <div className="absolute bottom-10 left-1/3 w-12 h-12 bg-pink-200 rounded-full opacity-20 animate-bounce-gentle" style={{animationDelay: '2s'}} />
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold gradient-text mb-4 animate-fade-in">
            ✨ 自动发卡商城
          </h1>
          <p className="text-lg md:text-xl text-gray-600 mb-8 animate-slide-up max-w-2xl mx-auto">
            安全、快速、便捷的数字商品购买体验，让您的购物更加轻松愉快
          </p>

          {/* 特色标签 */}
          <div className="flex flex-wrap justify-center items-center gap-6 mb-8">
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-md hover-lift">
              <Star className="w-5 h-5 mr-2 text-yellow-500" />
              <span className="font-medium text-gray-700">优质商品</span>
            </div>
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-md hover-lift">
              <Package className="w-5 h-5 mr-2 text-blue-500" />
              <span className="font-medium text-gray-700">即时发货</span>
            </div>
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-md hover-lift">
              <TrendingUp className="w-5 h-5 mr-2 text-green-500" />
              <span className="font-medium text-gray-700">安全支付</span>
            </div>
          </div>

          {/* 统计数据 */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-md mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold gradient-text">{products.length}</div>
              <div className="text-sm text-gray-500">商品种类</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold gradient-text">24/7</div>
              <div className="text-sm text-gray-500">在线服务</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold gradient-text">100%</div>
              <div className="text-sm text-gray-500">安全保障</div>
            </div>
          </div>
        </div>

        {/* 分类筛选 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">商品分类</h2>
          <div className="flex flex-wrap gap-3">
            <Button
              variant={selectedCategory === '' ? 'gradient' : 'outline'}
              onClick={() => handleCategoryChange('')}
              size="sm"
              className="font-medium"
            >
              <Package className="w-4 h-4 mr-2" />
              全部商品
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'gradient' : 'outline'}
                onClick={() => handleCategoryChange(category.id)}
                size="sm"
                className="font-medium"
              >
                {category.name}
                <Badge variant="secondary" className="ml-2 text-xs">
                  {category._count.products}
                </Badge>
              </Button>
            ))}
          </div>
        </div>

        {/* 商品列表 */}
        {loading ? (
          <div className="text-center py-16">
            <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              加载中...
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product, index) => (
              <Card key={product.id} className="group overflow-hidden animate-fade-in hover-lift card-hover" style={{animationDelay: `${index * 0.1}s`}}>
                <Link href={`/product/${product.id}`}>
                  <div className="relative overflow-hidden">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-48 object-cover cursor-pointer group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-gray-100 via-blue-50 to-purple-50 flex items-center justify-center group-hover:from-blue-100 group-hover:to-purple-100 transition-all duration-300">
                        <Package className="w-12 h-12 text-gray-400 group-hover:text-primary group-hover:scale-110 transition-all duration-300" />
                      </div>
                    )}

                    {/* 渐变遮罩 */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* 分类标签 */}
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="text-xs backdrop-blur-sm bg-white/80 hover:bg-white transition-colors">
                        {product.category.name}
                      </Badge>
                    </div>

                    {/* 库存状态 */}
                    <div className="absolute top-3 right-3">
                      {product._count.cards > 10 ? (
                        <Badge variant="success" className="text-xs backdrop-blur-sm">
                          充足
                        </Badge>
                      ) : product._count.cards > 0 ? (
                        <Badge variant="warning" className="text-xs backdrop-blur-sm">
                          紧缺
                        </Badge>
                      ) : (
                        <Badge variant="destructive" className="text-xs backdrop-blur-sm">
                          缺货
                        </Badge>
                      )}
                    </div>

                    {/* 缺货遮罩 */}
                    {product._count.cards === 0 && (
                      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center">
                        <div className="text-center text-white">
                          <Package className="w-8 h-8 mx-auto mb-2 opacity-60" />
                          <Badge variant="destructive" className="text-sm">暂时缺货</Badge>
                        </div>
                      </div>
                    )}

                    {/* 悬停时的快速操作 */}
                    <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <Button
                        size="sm"
                        variant="gradient"
                        className="w-full backdrop-blur-sm"
                        disabled={product._count.cards === 0}
                        onClick={(e) => {
                          e.preventDefault()
                          handleBuyNow(product.id)
                        }}
                      >
                        <ShoppingCart className="w-4 h-4 mr-2" />
                        {product._count.cards === 0 ? '缺货' : '立即购买'}
                      </Button>
                    </div>
                  </div>
                </Link>

                <CardContent className="p-5">
                  <Link href={`/product/${product.id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 cursor-pointer hover:text-primary transition-colors line-clamp-2 group-hover:text-primary">
                      {product.name}
                    </h3>
                  </Link>
                  {product.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">{product.description}</p>
                  )}

                  {/* 价格和评分 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex flex-col">
                      <div className="text-2xl font-bold gradient-text">
                        {formatPrice(product.price)}
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Star className="w-3 h-3 mr-1 text-yellow-400 fill-current" />
                        <span>4.8 (128)</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={product._count.cards > 10 ? "success" : product._count.cards > 0 ? "warning" : "destructive"} className="mb-1">
                        {product._count.cards} 张
                      </Badge>
                      <div className="text-xs text-gray-500">库存</div>
                    </div>
                  </div>

                  {/* 特性标签 */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    <Badge variant="outline" className="text-xs">
                      即时发货
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      7天退换
                    </Badge>
                    {product._count.cards > 50 && (
                      <Badge variant="success" className="text-xs">
                        热销
                      </Badge>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="p-5 pt-0 flex space-x-3">
                  <Link href={`/product/${product.id}`} className="flex-1">
                    <Button variant="outline" className="w-full hover-lift">
                      <Package className="w-4 h-4 mr-2" />
                      查看详情
                    </Button>
                  </Link>
                  <Button
                    variant="gradient"
                    className="flex-1 hover-lift"
                    disabled={product._count.cards === 0}
                    onClick={() => handleBuyNow(product.id)}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    {product._count.cards === 0 ? '缺货' : '立即购买'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {products.length === 0 && !loading && (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4 animate-bounce-gentle" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">暂无商品</h3>
              <p className="text-gray-500">当前分类下没有可用的商品，请尝试其他分类或稍后再来查看。</p>
            </div>
          </div>
        )}
      </main>

      {/* 页脚 */}
      <Footer />
    </div>
  )
}
