'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatPrice } from '@/lib/utils'
import { ArrowLeft, ShoppingCart, Minus, Plus, Package, Star, Shield, Clock } from 'lucide-react'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

export default function ProductPage() {
  const params = useParams()
  const router = useRouter()
  const productId = Array.isArray(params.productId) ? params.productId[0] : params.productId

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [quantity, setQuantity] = useState(1)
  const [error, setError] = useState('')

  useEffect(() => {
    if (productId) {
      fetchProduct()
    }
  }, [productId])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/products/${productId}/public`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
      } else {
        setError('商品不存在或已下架')
      }
    } catch (error) {
      setError('获取商品信息失败')
    } finally {
      setLoading(false)
    }
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && product && newQuantity <= product._count.cards) {
      setQuantity(newQuantity)
    }
  }

  const handleBuyNow = () => {
    if (product && quantity > 0) {
      router.push(`/checkout?productId=${product.id}&quantity=${quantity}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">商品不存在</h3>
          <p className="text-red-600 mb-6">{error || '商品不存在或已下架'}</p>
          <Link href="/">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  const totalPrice = product.price * quantity
  const isOutOfStock = product._count.cards === 0
  const maxQuantity = Math.min(product._count.cards, 10) // 限制最大购买数量

  return (
    <div className="min-h-screen bg-white">
      {/* 导航栏 */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>
            <div className="text-sm text-gray-600">
              商品详情
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回商品列表
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 商品图片 */}
          <div className="bg-white rounded-lg shadow-simple p-6">
            {product.image ? (
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-96 object-cover rounded-lg"
              />
            ) : (
              <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Package className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                  <span className="text-gray-500">暂无图片</span>
                </div>
              </div>
            )}
          </div>

          {/* 商品信息 */}
          <div className="bg-white rounded-lg shadow-simple p-6">
            <div className="mb-4">
              <span className="text-sm text-gray-600 font-medium">{product.category.name}</span>
              <h1 className="text-2xl font-bold text-gray-900 mt-1">{product.name}</h1>
            </div>

            {product.description && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-2">商品描述</h3>
                <p className="text-gray-600 leading-relaxed">{product.description}</p>
              </div>
            )}

            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">价格</span>
                <span className="text-2xl font-bold text-gray-900">{formatPrice(product.price)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">库存</span>
                <span className={`text-sm font-medium ${isOutOfStock ? 'text-red-600' : 'text-green-600'}`}>
                  {product._count.cards} 张
                </span>
              </div>
            </div>

            {!isOutOfStock && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-900 mb-2">
                  购买数量
                </label>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                  <span className="text-lg font-medium w-12 text-center">{quantity}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= maxQuantity}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  最多可购买 {maxQuantity} 张
                </p>
              </div>
            )}

            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-lg font-medium text-gray-900">总计</span>
                <span className="text-2xl font-bold text-gray-900">
                  {formatPrice(totalPrice)}
                </span>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleBuyNow}
                  disabled={isOutOfStock}
                  className="w-full"
                  size="lg"
                >
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  {isOutOfStock ? '暂时缺货' : '立即购买'}
                </Button>

                {!isOutOfStock && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">购买说明</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 支付成功后，卡密将立即自动发放</li>
                      <li>• 卡密将发送到您提供的邮箱地址</li>
                      <li>• 支持信用卡、借记卡等多种支付方式</li>
                      <li>• 所有交易均通过 Stripe 安全处理</li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 商品特性 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">服务保障</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <ShoppingCart className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-1">即时发货</h4>
              <p className="text-sm text-gray-600">支付成功后立即自动发放卡密</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">正品保证</h4>
              <p className="text-sm text-gray-600">所有卡密均为正品，可正常使用</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">安全支付</h4>
              <p className="text-sm text-gray-600">采用 Stripe 安全支付系统</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
