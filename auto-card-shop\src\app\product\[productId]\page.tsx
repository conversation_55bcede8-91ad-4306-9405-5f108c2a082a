'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatPrice } from '@/lib/utils'
import { ArrowLeft, ShoppingCart, Minus, Plus, Package, Star, Shield, Clock } from 'lucide-react'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

export default function ProductPage() {
  const params = useParams()
  const router = useRouter()
  const productId = Array.isArray(params.productId) ? params.productId[0] : params.productId

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [quantity, setQuantity] = useState(1)
  const [error, setError] = useState('')

  useEffect(() => {
    if (productId) {
      fetchProduct()
    }
  }, [productId])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/products/${productId}/public`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
      } else {
        setError('商品不存在或已下架')
      }
    } catch (error) {
      setError('获取商品信息失败')
    } finally {
      setLoading(false)
    }
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && product && newQuantity <= product._count.cards) {
      setQuantity(newQuantity)
    }
  }

  const handleBuyNow = () => {
    if (product && quantity > 0) {
      router.push(`/checkout?productId=${product.id}&quantity=${quantity}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            加载中...
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto text-center">
          <CardContent className="p-8">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">商品不存在</h3>
            <p className="text-red-600 mb-6">{error || '商品不存在或已下架'}</p>
            <Link href="/">
              <Button variant="gradient">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回首页
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const totalPrice = product.price * quantity
  const isOutOfStock = product._count.cards === 0
  const maxQuantity = Math.min(product._count.cards, 10) // 限制最大购买数量

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold gradient-text hover:scale-105 transition-transform">
                ✨ 自动发卡网站
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">商品详情</Badge>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6 animate-fade-in">
          <Link href="/">
            <Button variant="outline" size="sm" className="hover-lift">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回商品列表
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-up">
          {/* 商品图片 */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              {product.image ? (
                <div className="relative group">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              ) : (
                <div className="w-full h-96 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center">
                    <Package className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                    <span className="text-gray-500">暂无图片</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 商品信息 */}
          <Card className="h-fit">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Badge variant="gradient">{product.category.name}</Badge>
                {!isOutOfStock && (
                  <Badge variant="success">
                    <Package className="w-3 h-3 mr-1" />
                    现货
                  </Badge>
                )}
              </div>
              <CardTitle className="text-3xl gradient-text">{product.name}</CardTitle>
            </CardHeader>

            <CardContent className="space-y-6">
              {product.description && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Star className="w-4 h-4 mr-2 text-yellow-500" />
                    商品描述
                  </h3>
                  <p className="text-gray-600 leading-relaxed bg-gray-50 p-4 rounded-lg">{product.description}</p>
                </div>
              )}

              {/* 价格和库存信息 */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium text-gray-700">商品价格</span>
                  <span className="text-3xl font-bold gradient-text">{formatPrice(product.price)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">可用库存</span>
                  <Badge variant={product._count.cards > 10 ? "success" : product._count.cards > 0 ? "warning" : "destructive"}>
                    {product._count.cards} 张
                  </Badge>
                </div>
              </div>

              {/* 商品特性 */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <Shield className="w-6 h-6 text-green-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-green-800">安全保障</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <Clock className="w-6 h-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-blue-800">即时发货</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <Star className="w-6 h-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-purple-800">优质服务</div>
                </div>
              </div>

              {!isOutOfStock && (
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-3">
                    购买数量
                  </label>
                  <div className="flex items-center justify-center space-x-4 bg-white p-4 rounded-lg border">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                      className="hover-lift"
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                    <div className="text-center">
                      <div className="text-2xl font-bold gradient-text">{quantity}</div>
                      <div className="text-xs text-gray-500">张</div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={quantity >= maxQuantity}
                      className="hover-lift"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    最多可购买 {maxQuantity} 张
                  </p>
                </div>
              )}

              {/* 总计和购买按钮 */}
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border-2 border-purple-100">
                <div className="flex items-center justify-between mb-6">
                  <span className="text-lg font-semibold text-gray-900">总计金额</span>
                  <span className="text-3xl font-bold gradient-text">
                    {formatPrice(totalPrice)}
                  </span>
                </div>

                <Button
                  onClick={handleBuyNow}
                  disabled={isOutOfStock}
                  variant="gradient"
                  className="w-full"
                  size="xl"
                >
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  {isOutOfStock ? '暂时缺货' : '立即购买'}
                </Button>

                {!isOutOfStock && (
                  <div className="mt-4 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-blue-900 mb-3 flex items-center">
                      <Shield className="w-4 h-4 mr-2" />
                      购买说明
                    </h4>
                    <ul className="text-sm text-blue-800 space-y-2">
                      <li className="flex items-start">
                        <Clock className="w-3 h-3 mr-2 mt-0.5 text-green-600" />
                        支付成功后，卡密将立即自动发放
                      </li>
                      <li className="flex items-start">
                        <Package className="w-3 h-3 mr-2 mt-0.5 text-blue-600" />
                        卡密将发送到您提供的邮箱地址
                      </li>
                      <li className="flex items-start">
                        <Star className="w-3 h-3 mr-2 mt-0.5 text-purple-600" />
                        支持信用卡、借记卡等多种支付方式
                      </li>
                      <li className="flex items-start">
                        <Shield className="w-3 h-3 mr-2 mt-0.5 text-green-600" />
                        所有交易均通过 Stripe 安全处理
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 商品特性 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">服务保障</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <ShoppingCart className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-1">即时发货</h4>
              <p className="text-sm text-gray-600">支付成功后立即自动发放卡密</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">正品保证</h4>
              <p className="text-sm text-gray-600">所有卡密均为正品，可正常使用</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">安全支付</h4>
              <p className="text-sm text-gray-600">采用 Stripe 安全支付系统</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
