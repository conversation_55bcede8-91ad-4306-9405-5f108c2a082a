{"version": 3, "file": "index.js", "sources": ["../src/table-cell.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableCellOptions {\n  /**\n   * The HTML attributes for a table cell node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table cells.\n * @see https://www.tiptap.dev/api/nodes/table-cell\n */\nexport const TableCell = Node.create<TableCellOptions>({\n  name: 'tableCell',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth\n            ? colwidth.split(',').map(width => parseInt(width, 10))\n            : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [\n      { tag: 'td' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['td', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n})\n"], "names": [], "mappings": ";;AAWA;;;AAGG;AACU,MAAA,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;AACrD,IAAA,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,OAAO,EAAE,QAAQ;IAEjB,aAAa,GAAA;QACX,OAAO;AACL,YAAA,OAAO,EAAE;AACP,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AACD,YAAA,OAAO,EAAE;AACP,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,OAAO,IAAG;oBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;oBACjD,MAAM,KAAK,GAAG;0BACV,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;0BACpD,IAAI;AAER,oBAAA,OAAO,KAAK;iBACb;AACF,aAAA;SACF;KACF;AAED,IAAA,SAAS,EAAE,MAAM;AAEjB,IAAA,SAAS,EAAE,IAAI;IAEf,SAAS,GAAA;QACP,OAAO;YACL,EAAE,GAAG,EAAE,IAAI,EAAE;SACd;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC/E;AAEF,CAAA;;;;"}