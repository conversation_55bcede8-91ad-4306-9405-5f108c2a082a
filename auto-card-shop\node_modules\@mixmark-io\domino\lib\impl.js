"use strict";
var utils = require('./utils');

exports = module.exports = {
  CSSStyleDeclaration: require('./CSSStyleDeclaration'),
  CharacterData: require('./CharacterData'),
  Comment: require('./Comment'),
  DOMException: require('./DOMException'),
  DOMImplementation: require('./DOMImplementation'),
  DOMTokenList: require('./DOMTokenList'),
  Document: require('./Document'),
  DocumentFragment: require('./DocumentFragment'),
  DocumentType: require('./DocumentType'),
  Element: require('./Element'),
  HTMLParser: require('./HTMLParser'),
  NamedNodeMap: require('./NamedNodeMap'),
  Node: require('./Node'),
  NodeList: require('./NodeList'),
  NodeFilter: require('./NodeFilter'),
  ProcessingInstruction: require('./ProcessingInstruction'),
  Text: require('./Text'),
  Window: require('./Window')
};

utils.merge(exports, require('./events'));
utils.merge(exports, require('./htmlelts').elements);
utils.merge(exports, require('./svg').elements);
