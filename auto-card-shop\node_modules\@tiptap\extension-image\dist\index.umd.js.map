{"version": 3, "file": "index.umd.js", "sources": ["../src/image.ts"], "sourcesContent": ["import {\n  mergeAttributes,\n  Node,\n  nodeInputRule,\n} from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean,\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean,\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: { src: string, alt?: string, title?: string }) => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64\n          ? 'img[src]'\n          : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage: options => ({ commands }) => {\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [,, alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "nodeInputRule"], "mappings": ";;;;;;EA8CA;;EAEG;AACI,QAAM,UAAU,GAAG;EAE1B;;;EAGG;AACU,QAAA,KAAK,GAAGA,SAAI,CAAC,MAAM,CAAe;EAC7C,IAAA,IAAI,EAAE,OAAO;MAEb,UAAU,GAAA;UACR,OAAO;EACL,YAAA,MAAM,EAAE,KAAK;EACb,YAAA,WAAW,EAAE,KAAK;EAClB,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;MAED,MAAM,GAAA;EACJ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;OAC3B;MAED,KAAK,GAAA;EACH,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;OAChD;EAED,IAAA,SAAS,EAAE,IAAI;MAEf,aAAa,GAAA;UACX,OAAO;EACL,YAAA,GAAG,EAAE;EACH,gBAAA,OAAO,EAAE,IAAI;EACd,aAAA;EACD,YAAA,GAAG,EAAE;EACH,gBAAA,OAAO,EAAE,IAAI;EACd,aAAA;EACD,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,IAAI;EACd,aAAA;WACF;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC;EAChB,sBAAE;EACF,sBAAE,8BAA8B;EACnC,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,KAAK,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;OAC7E;MAED,WAAW,GAAA;UACT,OAAO;cACL,QAAQ,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACpC,OAAO,QAAQ,CAAC,aAAa,CAAC;sBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,oBAAA,KAAK,EAAE,OAAO;EACf,iBAAA,CAAC;eACH;WACF;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,UAAU;kBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;kBACf,aAAa,EAAE,KAAK,IAAG;sBACrB,MAAM,KAAI,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK;EAElC,oBAAA,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;mBAC3B;eACF,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;;"}