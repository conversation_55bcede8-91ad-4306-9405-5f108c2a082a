import TurndownService from 'turndown'

// HTML转Markdown的配置
const turndownService = new TurndownService({
  headingStyle: 'atx',
  hr: '---',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full',
})

// 自定义规则
turndownService.addRule('strikethrough', {
  filter: ['del', 's', 'strike'],
  replacement: function (content) {
    return '~~' + content + '~~'
  }
})

// 表格规则
turndownService.addRule('table', {
  filter: 'table',
  replacement: function (content) {
    return '\n\n' + content + '\n\n'
  }
})

turndownService.addRule('tableRow', {
  filter: 'tr',
  replacement: function (content, node) {
    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')
    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'
    
    if (isHeaderRow) {
      return '| ' + content + ' |\n| ' + borderCells + ' |'
    }
    return '| ' + content + ' |'
  }
})

turndownService.addRule('tableCell', {
  filter: ['th', 'td'],
  replacement: function (content) {
    return content.trim() + ' |'
  }
})

/**
 * 将HTML转换为Markdown
 */
export function htmlToMarkdown(html: string): string {
  if (!html) return ''
  
  try {
    return turndownService.turndown(html)
  } catch (error) {
    console.error('HTML to Markdown conversion failed:', error)
    return html
  }
}

/**
 * 将Markdown转换为HTML（简单实现）
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown) return ''
  
  try {
    let html = markdown
    
    // 标题
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')
    
    // 粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // 代码
    html = html.replace(/`(.*?)`/g, '<code>$1</code>')
    
    // 链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    
    // 列表
    html = html.replace(/^\s*\* (.+)$/gm, '<li>$1</li>')
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    
    html = html.replace(/^\s*\d+\. (.+)$/gm, '<li>$1</li>')
    html = html.replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>')
    
    // 引用
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')
    
    // 分隔线
    html = html.replace(/^---$/gm, '<hr>')
    
    // 段落
    html = html.replace(/\n\n/g, '</p><p>')
    html = '<p>' + html + '</p>'
    
    // 清理空段落
    html = html.replace(/<p><\/p>/g, '')
    html = html.replace(/<p>(<h[1-6]>)/g, '$1')
    html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1')
    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>)/g, '$1')
    html = html.replace(/(<\/ul>|<\/ol>|<\/blockquote>|<hr>)<\/p>/g, '$1')
    
    return html
  } catch (error) {
    console.error('Markdown to HTML conversion failed:', error)
    return markdown
  }
}

/**
 * 检测内容格式
 */
export function detectContentFormat(content: string): 'html' | 'markdown' | 'plain' {
  if (!content) return 'plain'

  // 更严格的HTML标签检测
  const htmlTagRegex = /<\/?[a-z][\s\S]*>/i
  const hasHtmlTags = htmlTagRegex.test(content)

  // 检测常见的HTML标签
  const commonHtmlTags = /<\/?(?:h[1-6]|p|div|span|strong|em|ul|ol|li|table|tr|td|th|blockquote|a|img|br|hr)\b[^>]*>/i

  if (hasHtmlTags && commonHtmlTags.test(content)) {
    return 'html'
  }

  // 检测Markdown语法
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 粗体
    /\*.*?\*/,               // 斜体
    /\[.*?\]\(.*?\)/,        // 链接
    /^[-*+]\s+/m,            // 无序列表
    /^\d+\.\s+/m,            // 有序列表
    /^>\s+/m,                // 引用
    /`.*?`/,                 // 行内代码
    /^```/m,                 // 代码块
    /^\|.*\|.*$/m,           // 表格
  ]

  if (markdownPatterns.some(pattern => pattern.test(content))) {
    return 'markdown'
  }

  return 'plain'
}

/**
 * 智能转换内容格式
 */
export function convertContent(content: string, targetFormat: 'html' | 'markdown'): string {
  const currentFormat = detectContentFormat(content)
  
  if (currentFormat === targetFormat) {
    return content
  }
  
  if (currentFormat === 'html' && targetFormat === 'markdown') {
    return htmlToMarkdown(content)
  }
  
  if (currentFormat === 'markdown' && targetFormat === 'html') {
    return markdownToHtml(content)
  }
  
  // 纯文本转换
  if (currentFormat === 'plain') {
    if (targetFormat === 'html') {
      return '<p>' + content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>') + '</p>'
    }
    return content
  }
  
  return content
}
