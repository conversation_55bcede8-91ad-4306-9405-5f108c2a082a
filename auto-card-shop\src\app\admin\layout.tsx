'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import {
  Package,
  ShoppingCart,
  CreditCard,
  Users,
  Settings,
  Home,
  Folder
} from 'lucide-react'

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            加载中...
          </div>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  const navigation = [
    { name: '首页', href: '/', icon: Home },
    { name: '仪表板', href: '/admin', icon: Home },
    { name: '分类管理', href: '/admin/categories', icon: Folder },
    { name: '商品管理', href: '/admin/products', icon: Package },
    { name: '订单管理', href: '/admin/orders', icon: ShoppingCart },
    { name: '卡密管理', href: '/admin/cards', icon: CreditCard },
    { name: '用户管理', href: '/admin/users', icon: Users },
    { name: '设置', href: '/admin/settings', icon: Settings },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="flex flex-col lg:flex-row">
        {/* 移动端顶部导航 */}
        <div className="lg:hidden bg-white/80 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-50">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-bold gradient-text">✨ 管理后台</h1>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {session.user.username?.charAt(0).toUpperCase()}
                </div>
              </div>
            </div>
          </div>
          {/* 移动端导航菜单 */}
          <div className="px-4 pb-4">
            <div className="grid grid-cols-2 xs:grid-cols-4 gap-2">
              {navigation.slice(1).map((item) => {
                const Icon = item.icon
                const isActive = typeof window !== 'undefined' && window.location.pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex flex-col items-center p-3 rounded-lg text-xs font-medium transition-all duration-200 ${
                      isActive
                        ? 'gradient-bg text-white shadow-md'
                        : 'text-gray-700 hover:bg-white/60 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="w-5 h-5 mb-1" />
                    <span className="text-center">{item.name}</span>
                  </Link>
                )
              })}
            </div>
          </div>
        </div>

        {/* 桌面端侧边栏 */}
        <div className="hidden lg:block w-64 bg-white/80 backdrop-blur-md shadow-elegant border-r border-white/20">
          <div className="p-6 border-b border-gray-100">
            <h1 className="text-xl font-bold gradient-text">✨ 管理后台</h1>
          </div>
          <nav className="mt-6 px-3">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = typeof window !== 'undefined' && window.location.pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-4 py-3 mb-2 rounded-lg text-sm font-medium transition-all duration-200 hover-lift ${
                    isActive
                      ? 'gradient-bg text-white shadow-md'
                      : 'text-gray-700 hover:bg-white/60 hover:text-gray-900'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>

        {/* 主内容区 */}
        <div className="flex-1 min-w-0">
          {/* 桌面端头部 */}
          <header className="hidden lg:block bg-white/80 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-40">
            <div className="px-6 py-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">
                  欢迎回来, <span className="gradient-text">{session.user.username}</span>
                </h2>
                <div className="flex items-center space-x-3">
                  <div className="hidden md:block text-sm text-gray-600 bg-white/50 px-3 py-1 rounded-full">
                    {session.user.email}
                  </div>
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    {session.user.username?.charAt(0).toUpperCase()}
                  </div>
                </div>
              </div>
            </div>
          </header>

          <main className="p-4 lg:p-6">
            <div className="animate-fade-in">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
