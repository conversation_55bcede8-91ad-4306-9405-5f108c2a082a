import Link from 'next/link'
import { <PERSON>, <PERSON>, Clock, Star } from 'lucide-react'

export function Footer() {
  return (
    <footer className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* 品牌信息 */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold gradient-text">✨ 自动发卡商城</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              专业的数字商品交易平台，为您提供安全、快速、便捷的购物体验。
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center text-sm text-gray-300">
                <Shield className="w-4 h-4 mr-2 text-green-400" />
                <span>安全保障</span>
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <Clock className="w-4 h-4 mr-2 text-blue-400" />
                <span>24/7服务</span>
              </div>
            </div>
          </div>

          {/* 快速链接 */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">快速链接</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors text-sm">
                  首页
                </Link>
              </li>
              <li>
                <Link href="/products" className="text-gray-300 hover:text-white transition-colors text-sm">
                  商品中心
                </Link>
              </li>
              <li>
                <Link href="/orders" className="text-gray-300 hover:text-white transition-colors text-sm">
                  我的订单
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-300 hover:text-white transition-colors text-sm">
                  帮助中心
                </Link>
              </li>
            </ul>
          </div>

          {/* 客户服务 */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">客户服务</h4>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors text-sm">
                  联系我们
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors text-sm">
                  常见问题
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors text-sm">
                  退换政策
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors text-sm">
                  隐私政策
                </a>
              </li>
            </ul>
          </div>

          {/* 联系信息 */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">联系我们</h4>
            <div className="space-y-3">
              <div className="text-sm text-gray-300">
                <strong>客服邮箱:</strong><br />
                <EMAIL>
              </div>
              <div className="text-sm text-gray-300">
                <strong>工作时间:</strong><br />
                周一至周日 9:00-21:00
              </div>
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-300">4.9/5.0 用户评分</span>
              </div>
            </div>
          </div>
        </div>

        {/* 分割线 */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © 2024 自动发卡商城. 保留所有权利.
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-400" />
              <span>by 开发团队</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
