@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 简约风格颜色主题 */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;
    --primary: 0 0% 9%; /* 简约黑色 */
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 9%;
    --radius: 0.5rem; /* 适中的圆角 */

    /* 简化的颜色变量 */
    --success: 142.1 76.2% 36.3%;
    --warning: 47.9 95.8% 53.1%;
    --info: 199.89 89.09% 48.43%;
  }

  .dark {
    --background: 0 0% 9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 83%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 平滑滚动 */
  html {
    scroll-behavior: smooth;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  /* 简约阴影效果 */
  .shadow-simple {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .shadow-simple-lg {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* 简单的动画类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
}

@layer utilities {
  /* 文本截断 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 简化的关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 简约悬停效果 */
.hover-simple {
  transition: all 0.2s ease-in-out;
}

.hover-simple:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Markdown内容样式优化 */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
  margin-top: 0;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul:last-child,
.markdown-content ol:last-child {
  margin-bottom: 0;
}

.markdown-content blockquote {
  margin: 1rem 0;
}

.markdown-content table {
  border-collapse: collapse;
}

.markdown-content pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

/* HTML内容样式 */
.markdown-content.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #1f2937;
}

.markdown-content.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #1f2937;
}

.markdown-content.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
}

.markdown-content.prose p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #374151;
}

.markdown-content.prose ul {
  list-style-type: disc;
  list-style-position: inside;
  margin: 0.75rem 0;
  padding-left: 1rem;
}

.markdown-content.prose ol {
  list-style-type: decimal;
  list-style-position: inside;
  margin: 0.75rem 0;
  padding-left: 1rem;
}

.markdown-content.prose li {
  margin: 0.25rem 0;
  color: #374151;
}

.markdown-content.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content.prose em {
  font-style: italic;
}

.markdown-content.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  color: #1f2937;
}

.markdown-content.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.markdown-content.prose a {
  color: #2563eb;
  text-decoration: underline;
}

.markdown-content.prose a:hover {
  color: #1d4ed8;
}

.markdown-content.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.markdown-content.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.markdown-content.prose th,
.markdown-content.prose td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.markdown-content.prose th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #1f2937;
}

.markdown-content.prose td {
  color: #374151;
}

.markdown-content.prose th:last-child,
.markdown-content.prose td:last-child {
  border-right: none;
}

.markdown-content.prose tr:last-child th,
.markdown-content.prose tr:last-child td {
  border-bottom: none;
}

.markdown-content.prose hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* 内容渲染器样式 */
.content-renderer h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #1f2937;
  line-height: 1.2;
}

.content-renderer h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #1f2937;
  line-height: 1.3;
}

.content-renderer h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
  line-height: 1.4;
}

.content-renderer h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0.875rem 0 0.5rem 0;
  color: #1f2937;
  line-height: 1.4;
}

.content-renderer h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.75rem 0 0.5rem 0;
  color: #1f2937;
  line-height: 1.5;
}

.content-renderer h6 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0.75rem 0 0.5rem 0;
  color: #4b5563;
  line-height: 1.5;
}

.content-renderer p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #374151;
}

.content-renderer ul {
  list-style-type: disc;
  list-style-position: inside;
  margin: 0.75rem 0;
  padding-left: 1rem;
}

.content-renderer ol {
  list-style-type: decimal;
  list-style-position: inside;
  margin: 0.75rem 0;
  padding-left: 1rem;
}

.content-renderer li {
  margin: 0.25rem 0;
  color: #374151;
  line-height: 1.5;
}

.content-renderer strong {
  font-weight: 600;
  color: #1f2937;
}

.content-renderer em {
  font-style: italic;
}

.content-renderer code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  color: #1f2937;
}

.content-renderer blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.content-renderer a {
  color: #2563eb;
  text-decoration: underline;
}

.content-renderer a:hover {
  color: #1d4ed8;
}

.content-renderer img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.content-renderer table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.content-renderer th,
.content-renderer td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.content-renderer th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #1f2937;
}

.content-renderer td {
  color: #374151;
}

.content-renderer th:last-child,
.content-renderer td:last-child {
  border-right: none;
}

.content-renderer tr:last-child th,
.content-renderer tr:last-child td {
  border-bottom: none;
}

.content-renderer hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* 确保第一个元素没有上边距 */
.content-renderer > *:first-child {
  margin-top: 0;
}

/* 确保最后一个元素没有下边距 */
.content-renderer > *:last-child {
  margin-bottom: 0;
}

/* 富文本编辑器样式 */
.ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: none;
  background: white;
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
}

.ProseMirror h1 { font-size: 1.875rem; }
.ProseMirror h2 { font-size: 1.5rem; }
.ProseMirror h3 { font-size: 1.25rem; }

.ProseMirror ul,
.ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.ProseMirror table th {
  background-color: #f9fafb;
  font-weight: 600;
}

.ProseMirror a {
  color: #2563eb;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 1.5rem 0;
}
