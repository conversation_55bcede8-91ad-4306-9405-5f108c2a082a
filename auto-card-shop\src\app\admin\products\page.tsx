'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Markdown } from '@/components/ui/markdown'
import { ContentPreview } from '@/components/ui/content-renderer'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import { formatPrice, formatDate } from '@/lib/utils'
import { htmlToMarkdown, markdownToHtml, detectContentFormat, convertContent } from '@/lib/content-converter'
import { Plus, Edit, Trash2, Eye, FileText, Type, Code } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  status: string
  stockCount: number
  createdAt: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

interface Category {
  id: string
  name: string
}

export default function ProductsManagement() {
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    categoryId: '',
    image: '',
    status: 'ACTIVE'
  })
  const [showMarkdownPreview, setShowMarkdownPreview] = useState(false)
  const [editorMode, setEditorMode] = useState<'rich' | 'markdown'>('rich')

  useEffect(() => {
    fetchProducts()
    fetchCategories()
  }, [])

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products?status=all')
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('获取商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingProduct ? `/api/products/${editingProduct.id}` : '/api/products'
      const method = editingProduct ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price)
        }),
      })

      if (response.ok) {
        await fetchProducts()
        resetForm()
        alert(editingProduct ? '商品更新成功' : '商品创建成功')
      } else {
        const error = await response.json()
        alert(error.error || '操作失败')
      }
    } catch (error) {
      alert('操作失败，请重试')
    }
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      description: product.description || '',
      price: product.price.toString(),
      categoryId: product.category.id || '',
      image: product.image || '',
      status: product.status
    })
    setShowAddForm(true)
  }

  const handleDelete = async (productId: string) => {
    if (!confirm('确定要删除这个商品吗？')) return

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchProducts()
        alert('商品删除成功')
      } else {
        alert('删除失败')
      }
    } catch (error) {
      alert('删除失败，请重试')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      categoryId: '',
      image: '',
      status: 'ACTIVE'
    })
    setEditingProduct(null)
    setShowAddForm(false)
    setShowMarkdownPreview(false)
    setEditorMode('rich')
  }

  // 切换编辑器模式
  const switchEditorMode = (newMode: 'rich' | 'markdown') => {
    if (newMode === editorMode) return

    let convertedContent = formData.description

    if (newMode === 'markdown' && editorMode === 'rich') {
      // 从富文本转换到Markdown
      convertedContent = htmlToMarkdown(formData.description)
    } else if (newMode === 'rich' && editorMode === 'markdown') {
      // 从Markdown转换到富文本
      convertedContent = markdownToHtml(formData.description)
    }

    setFormData({ ...formData, description: convertedContent })
    setEditorMode(newMode)
    setShowMarkdownPreview(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800'
      case 'OUT_OF_STOCK':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '上架'
      case 'INACTIVE':
        return '下架'
      case 'OUT_OF_STOCK':
        return '缺货'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">商品管理</h1>
          <p className="text-gray-600">管理网站上的所有商品</p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          添加商品
        </Button>
      </div>

      {/* 添加/编辑表单 */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">
            {editingProduct ? '编辑商品' : '添加商品'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  商品名称 *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  价格 *
                </label>
                <input
                  type="number"
                  step="0.01"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类 *
                </label>
                <select
                  required
                  value={formData.categoryId}
                  onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">选择分类</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="ACTIVE">上架</option>
                  <option value="INACTIVE">下架</option>
                  <option value="OUT_OF_STOCK">缺货</option>
                </select>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  商品描述
                </label>
                <div className="flex items-center space-x-2">
                  {/* 编辑器模式切换 */}
                  <div className="flex items-center bg-gray-100 rounded-md p-1">
                    <Button
                      type="button"
                      variant={editorMode === 'rich' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => switchEditorMode('rich')}
                      className="h-7 px-2 text-xs"
                    >
                      <Type className="w-3 h-3 mr-1" />
                      富文本
                    </Button>
                    <Button
                      type="button"
                      variant={editorMode === 'markdown' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => switchEditorMode('markdown')}
                      className="h-7 px-2 text-xs"
                    >
                      <Code className="w-3 h-3 mr-1" />
                      Markdown
                    </Button>
                  </div>

                  {/* 预览按钮（仅在Markdown模式下显示） */}
                  {editorMode === 'markdown' && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowMarkdownPreview(!showMarkdownPreview)}
                    >
                      <FileText className="w-4 h-4 mr-1" />
                      {showMarkdownPreview ? '编辑' : '预览'}
                    </Button>
                  )}
                </div>
              </div>

              {editorMode === 'rich' ? (
                <RichTextEditor
                  content={formData.description}
                  onChange={(content) => setFormData({ ...formData, description: content })}
                  placeholder="请输入商品描述..."
                />
              ) : showMarkdownPreview ? (
                <div className="w-full min-h-[200px] px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                  {formData.description ? (
                    <Markdown content={formData.description} />
                  ) : (
                    <p className="text-gray-500 italic">暂无内容</p>
                  )}
                </div>
              ) : (
                <textarea
                  rows={8}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                  placeholder="支持 Markdown 格式，例如：&#10;# 标题&#10;**粗体文字**&#10;- 列表项&#10;[链接](https://example.com)"
                />
              )}

              <div className="mt-2 text-xs text-gray-500">
                {editorMode === 'rich'
                  ? '使用富文本编辑器创建格式化内容，支持标题、列表、链接、表格等'
                  : '支持 Markdown 语法：标题 (#)、粗体 (**)、斜体 (*)、列表 (-)、链接 ([文字](URL)) 等'
                }
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                商品图片 URL
              </label>
              <input
                type="url"
                value={formData.image}
                onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            
            <div className="flex space-x-3">
              <Button type="submit">
                {editingProduct ? '更新商品' : '创建商品'}
              </Button>
              <Button type="button" variant="outline" onClick={resetForm}>
                取消
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* 商品列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  分类
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  库存
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {product.image && (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-10 h-10 rounded-lg object-cover mr-3"
                        />
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {product.name}
                        </div>
                        {product.description && (
                          <div className="text-sm text-gray-500 max-w-xs">
                            <ContentPreview content={product.description} maxLength={80} />
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.category.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                    {formatPrice(product.price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product._count.cards} 张
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                      {getStatusText(product.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(product.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(product)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(product.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {products.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500">暂无商品</div>
        </div>
      )}
    </div>
  )
}
