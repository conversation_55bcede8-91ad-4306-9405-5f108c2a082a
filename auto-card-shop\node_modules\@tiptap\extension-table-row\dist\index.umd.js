(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@tiptap/core')) :
  typeof define === 'function' && define.amd ? define(['exports', '@tiptap/core'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@tiptap/extension-table-row"] = {}, global.core));
})(this, (function (exports, core) { 'use strict';

  /**
   * This extension allows you to create table rows.
   * @see https://www.tiptap.dev/api/nodes/table-row
   */
  const TableRow = core.Node.create({
      name: 'tableRow',
      addOptions() {
          return {
              HTMLAttributes: {},
          };
      },
      content: '(tableCell | tableHeader)*',
      tableRole: 'row',
      parseHTML() {
          return [
              { tag: 'tr' },
          ];
      },
      renderHTML({ HTMLAttributes }) {
          return ['tr', core.mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
      },
  });

  exports.TableRow = TableRow;
  exports.default = TableRow;

  Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=index.umd.js.map
