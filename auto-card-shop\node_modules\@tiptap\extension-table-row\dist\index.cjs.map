{"version": 3, "file": "index.cjs", "sources": ["../src/table-row.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableRowOptions {\n  /**\n   * The HTML attributes for a table row node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table rows.\n * @see https://www.tiptap.dev/api/nodes/table-row\n */\nexport const TableRow = Node.create<TableRowOptions>({\n  name: 'tableRow',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: '(tableCell | tableHeader)*',\n\n  tableRole: 'row',\n\n  parseHTML() {\n    return [\n      { tag: 'tr' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['tr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;AAWA;;;AAGG;AACU,MAAA,QAAQ,GAAGA,SAAI,CAAC,MAAM,CAAkB;AACnD,IAAA,IAAI,EAAE,UAAU;IAEhB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,OAAO,EAAE,4BAA4B;AAErC,IAAA,SAAS,EAAE,KAAK;IAEhB,SAAS,GAAA;QACP,OAAO;YACL,EAAE,GAAG,EAAE,IAAI,EAAE;SACd;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC/E;AACF,CAAA;;;;;"}