'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { formatPrice } from '@/lib/utils'
import { loadStripe } from '@stripe/stripe-js'
import { ArrowLeft, CreditCard, Mail, ShoppingCart } from 'lucide-react'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  price: number
  image: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

export default function CheckoutPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const productId = searchParams.get('productId')
  const quantity = parseInt(searchParams.get('quantity') || '1')

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [email, setEmail] = useState('')
  const [error, setError] = useState('')

  useEffect(() => {
    if (productId) {
      fetchProduct()
    } else {
      setError('缺少商品信息')
      setLoading(false)
    }
  }, [productId])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/products/${productId}/public`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
      } else {
        setError('商品不存在')
      }
    } catch (error) {
      setError('获取商品信息失败')
    } finally {
      setLoading(false)
    }
  }

  const handlePayment = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      setError('请输入邮箱地址')
      return
    }

    if (!product) {
      setError('商品信息缺失')
      return
    }

    if (product._count.cards < quantity) {
      setError('库存不足')
      return
    }

    setProcessing(true)
    setError('')

    try {
      // 创建支付意图
      const response = await fetch('/api/payment/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: [{
            productId: product.id,
            quantity: quantity
          }],
          email: email
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '创建支付失败')
      }

      // 跳转到 Stripe Checkout
      if (data.sessionId) {
        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

        if (!stripe) {
          throw new Error('Stripe 初始化失败')
        }

        const { error: stripeError } = await stripe.redirectToCheckout({
          sessionId: data.sessionId
        })

        if (stripeError) {
          throw new Error(stripeError.message)
        }
      } else {
        throw new Error('支付会话创建失败')
      }

    } catch (error: any) {
      setError(error.message || '支付失败，请重试')
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error && !product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Link href="/">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  const totalAmount = product ? product.price * quantity : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>
            <div className="text-sm text-gray-600">
              结算页面
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 商品信息 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">订单详情</h2>
            
            {product && (
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  {product.image && (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 text-lg truncate">{product.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {product.category.name}
                      </span>
                    </p>
                  </div>
                </div>
                
                <div className="border-t pt-4 space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">单价</span>
                    <span className="font-medium">{formatPrice(product.price)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">数量</span>
                    <span className="font-medium">{quantity} 张</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">库存状态</span>
                    <span className={`text-sm font-medium ${product._count.cards < quantity ? 'text-red-600' : 'text-green-600'}`}>
                      {product._count.cards < quantity ? '库存不足' : '库存充足'}
                    </span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">总计</span>
                      <span className="text-xl font-bold text-red-600">{formatPrice(totalAmount)}</span>
                    </div>
                    <div className="text-xs text-gray-500 text-right mt-1">
                      {formatPrice(product.price)} × {quantity} 张
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 支付表单 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">支付信息</h2>
            
            <form onSubmit={handlePayment} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱地址 *
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    id="email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入邮箱地址"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  卡密将发送到此邮箱，请确保邮箱地址正确
                </p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-blue-900 mb-2">支付说明</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 支付成功后，卡密将立即发放</li>
                  <li>• 请确保邮箱地址正确，卡密将发送到此邮箱</li>
                  <li>• 支持信用卡、借记卡支付</li>
                  <li>• 所有交易均通过 Stripe 安全处理</li>
                </ul>
              </div>

              <div className="space-y-3">
                <Button
                  type="submit"
                  disabled={processing || !product || product._count.cards < quantity}
                  className="w-full"
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  {processing ? '处理中...' : `支付 ${formatPrice(totalAmount)}`}
                </Button>
                
                <Link href="/">
                  <Button variant="outline" className="w-full">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    返回商品页面
                  </Button>
                </Link>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  )
}
