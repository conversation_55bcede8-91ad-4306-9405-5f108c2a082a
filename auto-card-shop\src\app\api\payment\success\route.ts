import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')

    if (!sessionId) {
      return NextResponse.json(
        { error: '缺少支付会话ID' },
        { status: 400 }
      )
    }

    // 从 Stripe 获取支付会话信息
    const session = await stripe.checkout.sessions.retrieve(sessionId)

    if (!session.metadata?.orderId) {
      return NextResponse.json(
        { error: '订单信息缺失' },
        { status: 400 }
      )
    }

    const orderId = session.metadata.orderId

    console.log(`处理支付成功，订单ID: ${orderId}, 会话ID: ${sessionId}`)

    // 获取订单和卡密信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    if (!order) {
      console.error(`订单不存在: ${orderId}`)
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    console.log(`订单当前状态: ${order.status}`)

    // 如果订单还未支付或交付，先处理支付和发卡
    if (order.status === 'PENDING') {
      console.log('更新订单状态为已支付')
      // 首先更新订单状态为已支付
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'PAID',
          stripePaymentId: sessionId
        }
      })
    }

    if (order.status === 'PENDING' || order.status === 'PAID') {
      console.log('开始自动发卡流程')
      // 自动发卡逻辑
      for (const orderItem of order.orderItems) {
        // 获取可用的卡密
        const availableCards = await prisma.card.findMany({
          where: {
            productId: orderItem.productId,
            status: 'AVAILABLE'
          },
          take: orderItem.quantity,
          orderBy: {
            createdAt: 'asc'
          }
        })

        if (availableCards.length >= orderItem.quantity) {
          // 标记卡密为已售出
          await prisma.card.updateMany({
            where: {
              id: {
                in: availableCards.map(card => card.id)
              }
            },
            data: {
              status: 'SOLD',
              orderId: order.id,
              usedAt: new Date()
            }
          })

          // 更新商品库存
          await prisma.product.update({
            where: { id: orderItem.productId },
            data: {
              stockCount: {
                decrement: orderItem.quantity
              }
            }
          })
        }
      }

      // 更新订单状态为已交付
      console.log('更新订单状态为已交付')
      await prisma.order.update({
        where: { id: orderId },
        data: { status: 'DELIVERED' }
      })
    }

    // 重新获取订单信息，确保状态是最新的
    const updatedOrder = await prisma.order.findUnique({
      where: { id: orderId }
    })

    console.log(`最终订单状态: ${updatedOrder?.status}`)

    // 获取该订单的所有卡密
    const cards = await prisma.card.findMany({
      where: {
        orderId: orderId,
        status: 'SOLD'
      },
      include: {
        product: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // 按商品分组卡密
    const cardsByProduct = cards.reduce((acc, card) => {
      const productName = card.product.name
      if (!acc[productName]) {
        acc[productName] = []
      }
      acc[productName].push({
        id: card.id,
        cardData: card.cardData,
        usedAt: card.usedAt
      })
      return acc
    }, {} as Record<string, any[]>)

    return NextResponse.json({
      orderId: order.id,
      email: order.email,
      totalAmount: order.totalAmount,
      status: updatedOrder?.status || order.status,
      createdAt: order.createdAt,
      cards: cardsByProduct
    })
  } catch (error) {
    console.error('获取支付成功信息错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
