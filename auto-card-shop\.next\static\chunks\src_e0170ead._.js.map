{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-elegant hover-lift\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/content-converter.ts"], "sourcesContent": ["import TurndownService from 'turndown'\n\n// HTML转Markdown的配置\nconst turndownService = new TurndownService({\n  headingStyle: 'atx',\n  hr: '---',\n  bulletListMarker: '-',\n  codeBlockStyle: 'fenced',\n  fence: '```',\n  emDelimiter: '*',\n  strongDelimiter: '**',\n  linkStyle: 'inlined',\n  linkReferenceStyle: 'full',\n})\n\n// 自定义规则\nturndownService.addRule('strikethrough', {\n  filter: ['del', 's', 'strike'],\n  replacement: function (content) {\n    return '~~' + content + '~~'\n  }\n})\n\n// 表格规则\nturndownService.addRule('table', {\n  filter: 'table',\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n})\n\nturndownService.addRule('tableRow', {\n  filter: 'tr',\n  replacement: function (content, node) {\n    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')\n    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'\n    \n    if (isHeaderRow) {\n      return '| ' + content + ' |\\n| ' + borderCells + ' |'\n    }\n    return '| ' + content + ' |'\n  }\n})\n\nturndownService.addRule('tableCell', {\n  filter: ['th', 'td'],\n  replacement: function (content) {\n    return content.trim() + ' |'\n  }\n})\n\n/**\n * 将HTML转换为Markdown\n */\nexport function htmlToMarkdown(html: string): string {\n  if (!html) return ''\n  \n  try {\n    return turndownService.turndown(html)\n  } catch (error) {\n    console.error('HTML to Markdown conversion failed:', error)\n    return html\n  }\n}\n\n/**\n * 将Markdown转换为HTML（简单实现）\n */\nexport function markdownToHtml(markdown: string): string {\n  if (!markdown) return ''\n  \n  try {\n    let html = markdown\n    \n    // 标题\n    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')\n    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')\n    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')\n    \n    // 粗体和斜体\n    html = html.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n    html = html.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n    \n    // 代码\n    html = html.replace(/`(.*?)`/g, '<code>$1</code>')\n    \n    // 链接\n    html = html.replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\">$1</a>')\n    \n    // 列表\n    html = html.replace(/^\\s*\\* (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n    \n    html = html.replace(/^\\s*\\d+\\. (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ol>$1</ol>')\n    \n    // 引用\n    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')\n    \n    // 分隔线\n    html = html.replace(/^---$/gm, '<hr>')\n    \n    // 段落\n    html = html.replace(/\\n\\n/g, '</p><p>')\n    html = '<p>' + html + '</p>'\n    \n    // 清理空段落\n    html = html.replace(/<p><\\/p>/g, '')\n    html = html.replace(/<p>(<h[1-6]>)/g, '$1')\n    html = html.replace(/(<\\/h[1-6]>)<\\/p>/g, '$1')\n    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>)/g, '$1')\n    html = html.replace(/(<\\/ul>|<\\/ol>|<\\/blockquote>|<hr>)<\\/p>/g, '$1')\n    \n    return html\n  } catch (error) {\n    console.error('Markdown to HTML conversion failed:', error)\n    return markdown\n  }\n}\n\n/**\n * 检测内容格式\n */\nexport function detectContentFormat(content: string): 'html' | 'markdown' | 'plain' {\n  if (!content) return 'plain'\n\n  // 更严格的HTML标签检测\n  const htmlTagRegex = /<\\/?[a-z][\\s\\S]*>/i\n  const hasHtmlTags = htmlTagRegex.test(content)\n\n  // 检测常见的HTML标签\n  const commonHtmlTags = /<\\/?(?:h[1-6]|p|div|span|strong|em|ul|ol|li|table|tr|td|th|blockquote|a|img|br|hr)\\b[^>]*>/i\n\n  if (hasHtmlTags && commonHtmlTags.test(content)) {\n    return 'html'\n  }\n\n  // 检测Markdown语法\n  const markdownPatterns = [\n    /^#{1,6}\\s+/m,           // 标题\n    /\\*\\*.*?\\*\\*/,           // 粗体\n    /\\*.*?\\*/,               // 斜体\n    /\\[.*?\\]\\(.*?\\)/,        // 链接\n    /^[-*+]\\s+/m,            // 无序列表\n    /^\\d+\\.\\s+/m,            // 有序列表\n    /^>\\s+/m,                // 引用\n    /`.*?`/,                 // 行内代码\n    /^```/m,                 // 代码块\n    /^\\|.*\\|.*$/m,           // 表格\n  ]\n\n  if (markdownPatterns.some(pattern => pattern.test(content))) {\n    return 'markdown'\n  }\n\n  return 'plain'\n}\n\n/**\n * 智能转换内容格式\n */\nexport function convertContent(content: string, targetFormat: 'html' | 'markdown'): string {\n  const currentFormat = detectContentFormat(content)\n  \n  if (currentFormat === targetFormat) {\n    return content\n  }\n  \n  if (currentFormat === 'html' && targetFormat === 'markdown') {\n    return htmlToMarkdown(content)\n  }\n  \n  if (currentFormat === 'markdown' && targetFormat === 'html') {\n    return markdownToHtml(content)\n  }\n  \n  // 纯文本转换\n  if (currentFormat === 'plain') {\n    if (targetFormat === 'html') {\n      return '<p>' + content.replace(/\\n\\n/g, '</p><p>').replace(/\\n/g, '<br>') + '</p>'\n    }\n    return content\n  }\n  \n  return content\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,IAAI,+JAAA,CAAA,UAAe,CAAC;IAC1C,cAAc;IACd,IAAI;IACJ,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,oBAAoB;AACtB;AAEA,QAAQ;AACR,gBAAgB,OAAO,CAAC,iBAAiB;IACvC,QAAQ;QAAC;QAAO;QAAK;KAAS;IAC9B,aAAa,SAAU,OAAO;QAC5B,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,OAAO;AACP,gBAAgB,OAAO,CAAC,SAAS;IAC/B,QAAQ;IACR,aAAa,SAAU,OAAO;QAC5B,OAAO,SAAS,UAAU;IAC5B;AACF;AAEA,gBAAgB,OAAO,CAAC,YAAY;IAClC,QAAQ;IACR,aAAa,SAAU,OAAO,EAAE,IAAI;QAClC,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,IAAM,OAAO,IAAI,CAAC;QACtE,MAAM,cAAc,KAAK,UAAU,EAAE,aAAa;QAElD,IAAI,aAAa;YACf,OAAO,OAAO,UAAU,WAAW,cAAc;QACnD;QACA,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,gBAAgB,OAAO,CAAC,aAAa;IACnC,QAAQ;QAAC;QAAM;KAAK;IACpB,aAAa,SAAU,OAAO;QAC5B,OAAO,QAAQ,IAAI,KAAK;IAC1B;AACF;AAKO,SAAS,eAAe,IAAY;IACzC,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,OAAO,gBAAgB,QAAQ,CAAC;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,eAAe,QAAgB;IAC7C,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,IAAI,OAAO;QAEX,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,iBAAiB;QACrC,OAAO,KAAK,OAAO,CAAC,gBAAgB;QACpC,OAAO,KAAK,OAAO,CAAC,eAAe;QAEnC,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,YAAY;QAEhC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,4BAA4B;QAEhD,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,OAAO,KAAK,OAAO,CAAC,qBAAqB;QACzC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,MAAM;QACN,OAAO,KAAK,OAAO,CAAC,WAAW;QAE/B,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,SAAS;QAC7B,OAAO,QAAQ,OAAO;QAEtB,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,aAAa;QACjC,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,sBAAsB;QAC1C,OAAO,KAAK,OAAO,CAAC,qCAAqC;QACzD,OAAO,KAAK,OAAO,CAAC,6CAA6C;QAEjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAe;IACjD,IAAI,CAAC,SAAS,OAAO;IAErB,eAAe;IACf,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC;IAEtC,cAAc;IACd,MAAM,iBAAiB;IAEvB,IAAI,eAAe,eAAe,IAAI,CAAC,UAAU;QAC/C,OAAO;IACT;IAEA,eAAe;IACf,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,OAAe,EAAE,YAAiC;IAC/E,MAAM,gBAAgB,oBAAoB;IAE1C,IAAI,kBAAkB,cAAc;QAClC,OAAO;IACT;IAEA,IAAI,kBAAkB,UAAU,iBAAiB,YAAY;QAC3D,OAAO,eAAe;IACxB;IAEA,IAAI,kBAAkB,cAAc,iBAAiB,QAAQ;QAC3D,OAAO,eAAe;IACxB;IAEA,QAAQ;IACR,IAAI,kBAAkB,SAAS;QAC7B,IAAI,iBAAiB,QAAQ;YAC3B,OAAO,QAAQ,QAAQ,OAAO,CAAC,SAAS,WAAW,OAAO,CAAC,OAAO,UAAU;QAC9E;QACA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/markdown.tsx"], "sourcesContent": ["import React from 'react'\nimport ReactMarkdown from 'react-markdown'\nimport remarkGfm from 'remark-gfm'\nimport rehypeRaw from 'rehype-raw'\nimport { cn } from '@/lib/utils'\nimport { detectContentFormat } from '@/lib/content-converter'\n\ninterface MarkdownProps {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact'\n}\n\nexport function Markdown({ content, className, variant = 'default' }: MarkdownProps) {\n  const baseClasses = variant === 'compact'\n    ? 'text-sm text-gray-600 line-clamp-3'\n    : 'text-gray-700 leading-relaxed'\n\n  // 检测内容格式\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div\n        className={cn('markdown-content prose prose-sm max-w-none', baseClasses, className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  return (\n    <div className={cn('markdown-content', baseClasses, className)}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeRaw]}\n        components={{\n          // 标题样式\n          h1: ({ children }) => (\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4 mt-6 first:mt-0\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 mt-5 first:mt-0\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2 mt-4 first:mt-0\">\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-gray-700 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h6>\n          ),\n          \n          // 段落样式\n          p: ({ children }) => (\n            <p className=\"mb-4 last:mb-0\">\n              {children}\n            </p>\n          ),\n          \n          // 列表样式\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside mb-4 space-y-1\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside mb-4 space-y-1\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-gray-700\">\n              {children}\n            </li>\n          ),\n          \n          // 链接样式\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              className=\"text-blue-600 hover:text-blue-800 underline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // 强调样式\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-gray-900\">\n              {children}\n            </strong>\n          ),\n          em: ({ children }) => (\n            <em className=\"italic\">\n              {children}\n            </em>\n          ),\n          \n          // 代码样式\n          code: ({ children, className }) => {\n            const isInline = !className\n            if (isInline) {\n              return (\n                <code className=\"bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono\">\n                  {children}\n                </code>\n              )\n            }\n            return (\n              <code className=\"block bg-gray-100 text-gray-800 p-3 rounded text-sm font-mono overflow-x-auto\">\n                {children}\n              </code>\n            )\n          },\n          \n          // 引用样式\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-gray-300 pl-4 py-2 mb-4 italic text-gray-600 bg-gray-50\">\n              {children}\n            </blockquote>\n          ),\n          \n          // 分隔线样式\n          hr: () => (\n            <hr className=\"border-gray-300 my-6\" />\n          ),\n          \n          // 表格样式\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-4\">\n              <table className=\"min-w-full border border-gray-300\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-gray-50\">\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-white\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className=\"border-b border-gray-200\">\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className=\"px-4 py-2 text-left font-medium text-gray-900 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"px-4 py-2 text-gray-700 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </td>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n\n// 用于商品卡片的紧凑版本\nexport function MarkdownPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  // 移除Markdown语法，只保留纯文本用于预览\n  const plainText = content\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/>\\s+/g, '') // 移除引用标记\n    .replace(/[-*+]\\s+/g, '') // 移除列表标记\n    .replace(/\\n+/g, ' ') // 将换行替换为空格\n    .trim()\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAiB;IACjF,MAAM,cAAc,YAAY,YAC5B,uCACA;IAEJ,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C,aAAa;YACzE,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,aAAa;kBAClD,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,YAAY;gBACV,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAE,WAAU;kCACV;;;;;;gBAIL,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,WAAU;wBACV,QAAO;wBACP,KAAI;kCAEH;;;;;;gBAIL,OAAO;gBACP,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAU;kCACf;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;oBAC5B,MAAM,WAAW,CAAC;oBAClB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBACA,qBACE,6LAAC;wBAAK,WAAU;kCACb;;;;;;gBAGP;gBAEA,OAAO;gBACP,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAU;kCACnB;;;;;;gBAIL,QAAQ;gBACR,IAAI,kBACF,6LAAC;wBAAG,WAAU;;;;;;gBAGhB,OAAO;gBACP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;YAGP;sBAEC;;;;;;;;;;;AAIT;KA5KgB;AA+KT,SAAS,gBAAgB,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IACnG,0BAA0B;IAC1B,MAAM,YAAY,QACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;KAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,QAAQ,KAAK,WAAW;KAChC,IAAI;IAEP,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MAtBgB", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/content-renderer.tsx"], "sourcesContent": ["import React from 'react'\nimport { Markdown } from './markdown'\nimport { detectContentFormat } from '@/lib/content-converter'\nimport { cn } from '@/lib/utils'\n\ninterface ContentRendererProps {\n  content: string\n  className?: string\n}\n\nexport function ContentRenderer({ content, className }: ContentRendererProps) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div \n        className={cn('content-renderer prose prose-sm max-w-none', className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  // 如果是Markdown或纯文本，使用Markdown组件\n  return (\n    <Markdown content={content} className={className} />\n  )\n}\n\n// 用于商品卡片的简化版本\nexport function ContentPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n  \n  // 提取纯文本用于预览\n  let plainText = content\n  \n  if (contentFormat === 'html') {\n    // 移除HTML标签\n    plainText = content\n      .replace(/<[^>]*>/g, '') // 移除所有HTML标签\n      .replace(/&nbsp;/g, ' ') // 替换HTML实体\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#39;/g, \"'\")\n      .replace(/\\s+/g, ' ') // 将多个空白字符替换为单个空格\n      .trim()\n  } else if (contentFormat === 'markdown') {\n    // 移除Markdown语法\n    plainText = content\n      .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n      .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n      .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n      .replace(/`(.*?)`/g, '$1') // 移除代码标记\n      .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n      .replace(/>\\s+/g, '') // 移除引用标记\n      .replace(/[-*+]\\s+/g, '') // 移除列表标记\n      .replace(/\\d+\\.\\s+/g, '') // 移除有序列表标记\n      .replace(/\\|.*?\\|/g, '') // 移除表格\n      .replace(/\\n+/g, ' ') // 将换行替换为空格\n      .trim()\n  }\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAOO,SAAS,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAwB;IAC1E,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;YAC5D,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,+BAA+B;IAC/B,qBACE,6LAAC,uIAAA,CAAA,WAAQ;QAAC,SAAS;QAAS,WAAW;;;;;;AAE3C;KArBgB;AAwBT,SAAS,eAAe,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IAClG,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,YAAY;IACZ,IAAI,YAAY;IAEhB,IAAI,kBAAkB,QAAQ;QAC5B,WAAW;QACX,YAAY,QACT,OAAO,CAAC,YAAY,IAAI,aAAa;SACrC,OAAO,CAAC,WAAW,KAAK,WAAW;SACnC,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,QAAQ,KAAK,iBAAiB;SACtC,IAAI;IACT,OAAO,IAAI,kBAAkB,YAAY;QACvC,eAAe;QACf,YAAY,QACT,OAAO,CAAC,cAAc,IAAI,SAAS;SACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;SACzC,OAAO,CAAC,cAAc,MAAM,SAAS;SACrC,OAAO,CAAC,YAAY,MAAM,SAAS;SACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;SAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;SAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;SAClC,OAAO,CAAC,aAAa,IAAI,WAAW;SACpC,OAAO,CAAC,YAAY,IAAI,OAAO;SAC/B,OAAO,CAAC,QAAQ,KAAK,WAAW;SAChC,IAAI;IACT;IAEA,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MA/CgB", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ContentPreview } from '@/components/ui/content-renderer'\nimport { formatPrice } from '@/lib/utils'\nimport { ShoppingCart, User, LogIn, Package } from 'lucide-react'\n\ninterface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image: string\n  category: {\n    name: string\n  }\n  _count: {\n    cards: number\n  }\n}\n\ninterface Category {\n  id: string\n  name: string\n  slug: string\n  _count: {\n    products: number\n  }\n}\n\nexport default function Home() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [products, setProducts] = useState<Product[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchCategories()\n    fetchProducts()\n  }, [])\n\n  const fetchCategories = async () => {\n    try {\n      const response = await fetch('/api/categories')\n      const data = await response.json()\n      setCategories(data)\n    } catch (error) {\n      console.error('获取分类失败:', error)\n    }\n  }\n\n  const fetchProducts = async (categoryId?: string) => {\n    try {\n      const url = categoryId\n        ? `/api/products?categoryId=${categoryId}`\n        : '/api/products'\n      const response = await fetch(url)\n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('获取商品失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCategoryChange = (categoryId: string) => {\n    setSelectedCategory(categoryId)\n    setLoading(true)\n    fetchProducts(categoryId || undefined)\n  }\n\n  const handleBuyNow = (productId: string) => {\n    router.push(`/checkout?productId=${productId}&quantity=1`)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {session ? (\n                <>\n                  <Link href=\"/orders\" className=\"text-gray-700 hover:text-gray-900 transition-colors\">\n                    我的订单\n                  </Link>\n                  <div className=\"flex items-center space-x-2\">\n                    <User className=\"w-4 h-4 text-gray-600\" />\n                    <span className=\"text-sm text-gray-700\">{session.user.username}</span>\n                  </div>\n                </>\n              ) : (\n                <Link href=\"/auth/signin\">\n                  <Button size=\"sm\">\n                    <LogIn className=\"w-4 h-4 mr-2\" />\n                    登录\n                  </Button>\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 简约标题 */}\n        <div className=\"mb-12 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            自动发卡商城\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            安全、快速、便捷的数字商品购买体验\n          </p>\n        </div>\n\n        {/* 分类筛选 */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-wrap gap-2\">\n            <Button\n              variant={selectedCategory === '' ? 'default' : 'outline'}\n              onClick={() => handleCategoryChange('')}\n              size=\"sm\"\n            >\n              全部商品\n            </Button>\n            {categories.map((category) => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'default' : 'outline'}\n                onClick={() => handleCategoryChange(category.id)}\n                size=\"sm\"\n              >\n                {category.name} ({category._count.products})\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* 商品列表 */}\n        {loading ? (\n          <div className=\"text-center py-16\">\n            <div className=\"text-gray-500\">加载中...</div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {products.map((product) => (\n              <Card key={product.id} className=\"overflow-hidden hover-simple\">\n                <Link href={`/product/${product.id}`}>\n                  <div className=\"relative\">\n                    {product.image ? (\n                      <img\n                        src={product.image}\n                        alt={product.name}\n                        className=\"w-full h-48 object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-48 bg-gray-100 flex items-center justify-center\">\n                        <Package className=\"w-12 h-12 text-gray-400\" />\n                      </div>\n                    )}\n                    {product._count.cards === 0 && (\n                      <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n                        <span className=\"text-white text-sm font-medium\">缺货</span>\n                      </div>\n                    )}\n                  </div>\n                </Link>\n\n                <CardContent className=\"p-4\">\n                  <div className=\"text-sm text-gray-500 mb-1\">{product.category.name}</div>\n                  <Link href={`/product/${product.id}`}>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2 hover:text-gray-700 transition-colors\">\n                      {product.name}\n                    </h3>\n                  </Link>\n                  {product.description && (\n                    <div className=\"mb-3\">\n                      <ContentPreview content={product.description} maxLength={120} />\n                    </div>\n                  )}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-xl font-bold text-gray-900\">\n                      {formatPrice(product.price)}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      库存: {product._count.cards}\n                    </div>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Link href={`/product/${product.id}`} className=\"flex-1\">\n                      <Button variant=\"outline\" className=\"w-full\">\n                        查看详情\n                      </Button>\n                    </Link>\n                    <Button\n                      className=\"flex-1\"\n                      disabled={product._count.cards === 0}\n                      onClick={() => handleBuyNow(product.id)}\n                    >\n                      <ShoppingCart className=\"w-4 h-4 mr-2\" />\n                      {product._count.cards === 0 ? '缺货' : '购买'}\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {products.length === 0 && !loading && (\n          <div className=\"text-center py-16\">\n            <Package className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">暂无商品</h3>\n            <p className=\"text-gray-500\">当前分类下没有可用的商品，请尝试其他分类或稍后再来查看。</p>\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAmCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;YACA;QACF;yBAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,MAAM,aACR,CAAC,yBAAyB,EAAE,YAAY,GACxC;YACJ,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,WAAW;QACX,cAAc,cAAc;IAC9B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,UAAU,WAAW,CAAC;IAC3D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;0CACZ,wBACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAyB,QAAQ,IAAI,CAAC,QAAQ;;;;;;;;;;;;;iEAIlE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;;0DACX,6LAAC,2MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhD,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,qBAAqB,KAAK,YAAY;oCAC/C,SAAS,IAAM,qBAAqB;oCACpC,MAAK;8CACN;;;;;;gCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,SAAM;wCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;wCACxD,SAAS,IAAM,qBAAqB,SAAS,EAAE;wCAC/C,MAAK;;4CAEJ,SAAS,IAAI;4CAAC;4CAAG,SAAS,MAAM,CAAC,QAAQ;4CAAC;;uCALtC,SAAS,EAAE;;;;;;;;;;;;;;;;oBAYvB,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;6CAGjC,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;kDAClC,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,iBACZ,6LAAC;oDACC,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,IAAI;oDACjB,WAAU;;;;;yEAGZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;gDAGtB,QAAQ,MAAM,CAAC,KAAK,KAAK,mBACxB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;kDAMzD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;0DAA8B,QAAQ,QAAQ,CAAC,IAAI;;;;;;0DAClE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;0DAClC,cAAA,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;;;;;;4CAGhB,QAAQ,WAAW,kBAClB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,kJAAA,CAAA,iBAAc;oDAAC,SAAS,QAAQ,WAAW;oDAAE,WAAW;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;4DAAwB;4DAChC,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;wDAAE,WAAU;kEAC9C,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;sEAAS;;;;;;;;;;;kEAI/C,6LAAC,qIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,UAAU,QAAQ,MAAM,CAAC,KAAK,KAAK;wDACnC,SAAS,IAAM,aAAa,QAAQ,EAAE;;0EAEtC,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,QAAQ,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;+BAtDlC,QAAQ,EAAE;;;;;;;;;;oBA+D1B,SAAS,MAAM,KAAK,KAAK,CAAC,yBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAxMwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}