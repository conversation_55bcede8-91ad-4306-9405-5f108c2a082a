{"version": 3, "file": "nodePasteRule.d.ts", "sourceRoot": "", "sources": ["../../src/pasteRules/nodePasteRule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAA;AAC5D,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,MAAM,aAAa,CAAA;AAGnE;;;;GAIG;AACH,wBAAgB,aAAa,CAAC,MAAM,EAAE;IACpC,IAAI,EAAE,eAAe,CAAA;IACrB,IAAI,EAAE,QAAQ,CAAA;IACd,aAAa,CAAC,EACV,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GACnB,CAAC,CAAC,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE,cAAc,KAAK,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GACjF,KAAK,GACL,IAAI,CAAA;IACR,UAAU,CAAC,EACP,WAAW,EAAE,GACb,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC,GAC/C,KAAK,GACL,IAAI,CAAA;CACT,aAwBA"}