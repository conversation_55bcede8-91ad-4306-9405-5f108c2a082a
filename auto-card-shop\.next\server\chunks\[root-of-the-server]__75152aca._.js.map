{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, username, password } = await request.json()\n\n    // 验证输入\n    if (!email || !username || !password) {\n      return NextResponse.json(\n        { error: '所有字段都是必需的' },\n        { status: 400 }\n      )\n    }\n\n    // 检查用户是否已存在\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { email },\n          { username }\n        ]\n      }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: '用户已存在' },\n        { status: 400 }\n      )\n    }\n\n    // 加密密码\n    const hashedPassword = await bcrypt.hash(password, 12)\n\n    // 创建用户\n    const user = await prisma.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n      }\n    })\n\n    // 返回用户信息（不包含密码）\n    const { password: _, ...userWithoutPassword } = user\n\n    return NextResponse.json(\n      { user: userWithoutPassword, message: '用户创建成功' },\n      { status: 201 }\n    )\n  } catch (error) {\n    console.error('注册错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAExD,OAAO;QACP,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE;oBAAM;oBACR;wBAAE;oBAAS;iBACZ;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,OAAO;QACP,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;YACZ;QACF;QAEA,gBAAgB;QAChB,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,MAAM;YAAqB,SAAS;QAAS,GAC/C;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}