{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/content-converter.ts"], "sourcesContent": ["import TurndownService from 'turndown'\n\n// HTML转Markdown的配置\nconst turndownService = new TurndownService({\n  headingStyle: 'atx',\n  hr: '---',\n  bulletListMarker: '-',\n  codeBlockStyle: 'fenced',\n  fence: '```',\n  emDelimiter: '*',\n  strongDelimiter: '**',\n  linkStyle: 'inlined',\n  linkReferenceStyle: 'full',\n})\n\n// 自定义规则\nturndownService.addRule('strikethrough', {\n  filter: ['del', 's', 'strike'],\n  replacement: function (content) {\n    return '~~' + content + '~~'\n  }\n})\n\n// 表格规则\nturndownService.addRule('table', {\n  filter: 'table',\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n})\n\nturndownService.addRule('tableRow', {\n  filter: 'tr',\n  replacement: function (content, node) {\n    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')\n    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'\n    \n    if (isHeaderRow) {\n      return '| ' + content + ' |\\n| ' + borderCells + ' |'\n    }\n    return '| ' + content + ' |'\n  }\n})\n\nturndownService.addRule('tableCell', {\n  filter: ['th', 'td'],\n  replacement: function (content) {\n    return content.trim() + ' |'\n  }\n})\n\n/**\n * 将HTML转换为Markdown\n */\nexport function htmlToMarkdown(html: string): string {\n  if (!html) return ''\n  \n  try {\n    return turndownService.turndown(html)\n  } catch (error) {\n    console.error('HTML to Markdown conversion failed:', error)\n    return html\n  }\n}\n\n/**\n * 将Markdown转换为HTML（简单实现）\n */\nexport function markdownToHtml(markdown: string): string {\n  if (!markdown) return ''\n  \n  try {\n    let html = markdown\n    \n    // 标题\n    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')\n    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')\n    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')\n    \n    // 粗体和斜体\n    html = html.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n    html = html.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n    \n    // 代码\n    html = html.replace(/`(.*?)`/g, '<code>$1</code>')\n    \n    // 链接\n    html = html.replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\">$1</a>')\n    \n    // 列表\n    html = html.replace(/^\\s*\\* (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n    \n    html = html.replace(/^\\s*\\d+\\. (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ol>$1</ol>')\n    \n    // 引用\n    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')\n    \n    // 分隔线\n    html = html.replace(/^---$/gm, '<hr>')\n    \n    // 段落\n    html = html.replace(/\\n\\n/g, '</p><p>')\n    html = '<p>' + html + '</p>'\n    \n    // 清理空段落\n    html = html.replace(/<p><\\/p>/g, '')\n    html = html.replace(/<p>(<h[1-6]>)/g, '$1')\n    html = html.replace(/(<\\/h[1-6]>)<\\/p>/g, '$1')\n    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>)/g, '$1')\n    html = html.replace(/(<\\/ul>|<\\/ol>|<\\/blockquote>|<hr>)<\\/p>/g, '$1')\n    \n    return html\n  } catch (error) {\n    console.error('Markdown to HTML conversion failed:', error)\n    return markdown\n  }\n}\n\n/**\n * 检测内容格式\n */\nexport function detectContentFormat(content: string): 'html' | 'markdown' | 'plain' {\n  if (!content) return 'plain'\n\n  // 更严格的HTML标签检测\n  const htmlTagRegex = /<\\/?[a-z][\\s\\S]*>/i\n  const hasHtmlTags = htmlTagRegex.test(content)\n\n  // 检测常见的HTML标签\n  const commonHtmlTags = /<\\/?(?:h[1-6]|p|div|span|strong|em|ul|ol|li|table|tr|td|th|blockquote|a|img|br|hr)\\b[^>]*>/i\n\n  if (hasHtmlTags && commonHtmlTags.test(content)) {\n    return 'html'\n  }\n\n  // 检测Markdown语法\n  const markdownPatterns = [\n    /^#{1,6}\\s+/m,           // 标题\n    /\\*\\*.*?\\*\\*/,           // 粗体\n    /\\*.*?\\*/,               // 斜体\n    /\\[.*?\\]\\(.*?\\)/,        // 链接\n    /^[-*+]\\s+/m,            // 无序列表\n    /^\\d+\\.\\s+/m,            // 有序列表\n    /^>\\s+/m,                // 引用\n    /`.*?`/,                 // 行内代码\n    /^```/m,                 // 代码块\n    /^\\|.*\\|.*$/m,           // 表格\n  ]\n\n  if (markdownPatterns.some(pattern => pattern.test(content))) {\n    return 'markdown'\n  }\n\n  return 'plain'\n}\n\n/**\n * 智能转换内容格式\n */\nexport function convertContent(content: string, targetFormat: 'html' | 'markdown'): string {\n  const currentFormat = detectContentFormat(content)\n  \n  if (currentFormat === targetFormat) {\n    return content\n  }\n  \n  if (currentFormat === 'html' && targetFormat === 'markdown') {\n    return htmlToMarkdown(content)\n  }\n  \n  if (currentFormat === 'markdown' && targetFormat === 'html') {\n    return markdownToHtml(content)\n  }\n  \n  // 纯文本转换\n  if (currentFormat === 'plain') {\n    if (targetFormat === 'html') {\n      return '<p>' + content.replace(/\\n\\n/g, '</p><p>').replace(/\\n/g, '<br>') + '</p>'\n    }\n    return content\n  }\n  \n  return content\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,IAAI,+JAAA,CAAA,UAAe,CAAC;IAC1C,cAAc;IACd,IAAI;IACJ,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,oBAAoB;AACtB;AAEA,QAAQ;AACR,gBAAgB,OAAO,CAAC,iBAAiB;IACvC,QAAQ;QAAC;QAAO;QAAK;KAAS;IAC9B,aAAa,SAAU,OAAO;QAC5B,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,OAAO;AACP,gBAAgB,OAAO,CAAC,SAAS;IAC/B,QAAQ;IACR,aAAa,SAAU,OAAO;QAC5B,OAAO,SAAS,UAAU;IAC5B;AACF;AAEA,gBAAgB,OAAO,CAAC,YAAY;IAClC,QAAQ;IACR,aAAa,SAAU,OAAO,EAAE,IAAI;QAClC,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,IAAM,OAAO,IAAI,CAAC;QACtE,MAAM,cAAc,KAAK,UAAU,EAAE,aAAa;QAElD,IAAI,aAAa;YACf,OAAO,OAAO,UAAU,WAAW,cAAc;QACnD;QACA,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,gBAAgB,OAAO,CAAC,aAAa;IACnC,QAAQ;QAAC;QAAM;KAAK;IACpB,aAAa,SAAU,OAAO;QAC5B,OAAO,QAAQ,IAAI,KAAK;IAC1B;AACF;AAKO,SAAS,eAAe,IAAY;IACzC,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,OAAO,gBAAgB,QAAQ,CAAC;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,eAAe,QAAgB;IAC7C,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,IAAI,OAAO;QAEX,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,iBAAiB;QACrC,OAAO,KAAK,OAAO,CAAC,gBAAgB;QACpC,OAAO,KAAK,OAAO,CAAC,eAAe;QAEnC,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,YAAY;QAEhC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,4BAA4B;QAEhD,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,OAAO,KAAK,OAAO,CAAC,qBAAqB;QACzC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,MAAM;QACN,OAAO,KAAK,OAAO,CAAC,WAAW;QAE/B,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,SAAS;QAC7B,OAAO,QAAQ,OAAO;QAEtB,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,aAAa;QACjC,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,sBAAsB;QAC1C,OAAO,KAAK,OAAO,CAAC,qCAAqC;QACzD,OAAO,KAAK,OAAO,CAAC,6CAA6C;QAEjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAe;IACjD,IAAI,CAAC,SAAS,OAAO;IAErB,eAAe;IACf,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC;IAEtC,cAAc;IACd,MAAM,iBAAiB;IAEvB,IAAI,eAAe,eAAe,IAAI,CAAC,UAAU;QAC/C,OAAO;IACT;IAEA,eAAe;IACf,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,OAAe,EAAE,YAAiC;IAC/E,MAAM,gBAAgB,oBAAoB;IAE1C,IAAI,kBAAkB,cAAc;QAClC,OAAO;IACT;IAEA,IAAI,kBAAkB,UAAU,iBAAiB,YAAY;QAC3D,OAAO,eAAe;IACxB;IAEA,IAAI,kBAAkB,cAAc,iBAAiB,QAAQ;QAC3D,OAAO,eAAe;IACxB;IAEA,QAAQ;IACR,IAAI,kBAAkB,SAAS;QAC7B,IAAI,iBAAiB,QAAQ;YAC3B,OAAO,QAAQ,QAAQ,OAAO,CAAC,SAAS,WAAW,OAAO,CAAC,OAAO,UAAU;QAC9E;QACA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/markdown.tsx"], "sourcesContent": ["import React from 'react'\nimport ReactMarkdown from 'react-markdown'\nimport remarkGfm from 'remark-gfm'\nimport rehypeRaw from 'rehype-raw'\nimport { cn } from '@/lib/utils'\nimport { detectContentFormat } from '@/lib/content-converter'\n\ninterface MarkdownProps {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact'\n}\n\nexport function Markdown({ content, className, variant = 'default' }: MarkdownProps) {\n  const baseClasses = variant === 'compact'\n    ? 'text-sm text-gray-600 line-clamp-3'\n    : 'text-gray-700 leading-relaxed'\n\n  // 检测内容格式\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div\n        className={cn('markdown-content prose prose-sm max-w-none', baseClasses, className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  return (\n    <div className={cn('markdown-content', baseClasses, className)}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeRaw]}\n        components={{\n          // 标题样式\n          h1: ({ children }) => (\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4 mt-6 first:mt-0\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 mt-5 first:mt-0\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2 mt-4 first:mt-0\">\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-gray-700 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h6>\n          ),\n          \n          // 段落样式\n          p: ({ children }) => (\n            <p className=\"mb-4 last:mb-0\">\n              {children}\n            </p>\n          ),\n          \n          // 列表样式\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside mb-4 space-y-1\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside mb-4 space-y-1\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-gray-700\">\n              {children}\n            </li>\n          ),\n          \n          // 链接样式\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              className=\"text-blue-600 hover:text-blue-800 underline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // 强调样式\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-gray-900\">\n              {children}\n            </strong>\n          ),\n          em: ({ children }) => (\n            <em className=\"italic\">\n              {children}\n            </em>\n          ),\n          \n          // 代码样式\n          code: ({ children, className }) => {\n            const isInline = !className\n            if (isInline) {\n              return (\n                <code className=\"bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono\">\n                  {children}\n                </code>\n              )\n            }\n            return (\n              <code className=\"block bg-gray-100 text-gray-800 p-3 rounded text-sm font-mono overflow-x-auto\">\n                {children}\n              </code>\n            )\n          },\n          \n          // 引用样式\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-gray-300 pl-4 py-2 mb-4 italic text-gray-600 bg-gray-50\">\n              {children}\n            </blockquote>\n          ),\n          \n          // 分隔线样式\n          hr: () => (\n            <hr className=\"border-gray-300 my-6\" />\n          ),\n          \n          // 表格样式\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-4\">\n              <table className=\"min-w-full border border-gray-300\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-gray-50\">\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-white\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className=\"border-b border-gray-200\">\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className=\"px-4 py-2 text-left font-medium text-gray-900 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"px-4 py-2 text-gray-700 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </td>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n\n// 用于商品卡片的紧凑版本\nexport function MarkdownPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  // 移除Markdown语法，只保留纯文本用于预览\n  const plainText = content\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/>\\s+/g, '') // 移除引用标记\n    .replace(/[-*+]\\s+/g, '') // 移除列表标记\n    .replace(/\\n+/g, ' ') // 将换行替换为空格\n    .trim()\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAiB;IACjF,MAAM,cAAc,YAAY,YAC5B,uCACA;IAEJ,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C,aAAa;YACzE,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,aAAa;kBAClD,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,YAAY;gBACV,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAE,WAAU;kCACV;;;;;;gBAIL,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,WAAU;wBACV,QAAO;wBACP,KAAI;kCAEH;;;;;;gBAIL,OAAO;gBACP,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAU;kCACf;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;oBAC5B,MAAM,WAAW,CAAC;oBAClB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBACA,qBACE,6LAAC;wBAAK,WAAU;kCACb;;;;;;gBAGP;gBAEA,OAAO;gBACP,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAU;kCACnB;;;;;;gBAIL,QAAQ;gBACR,IAAI,kBACF,6LAAC;wBAAG,WAAU;;;;;;gBAGhB,OAAO;gBACP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;YAGP;sBAEC;;;;;;;;;;;AAIT;KA5KgB;AA+KT,SAAS,gBAAgB,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IACnG,0BAA0B;IAC1B,MAAM,YAAY,QACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;KAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,QAAQ,KAAK,WAAW;KAChC,IAAI;IAEP,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MAtBgB", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/content-renderer.tsx"], "sourcesContent": ["import React from 'react'\nimport { Markdown } from './markdown'\nimport { detectContentFormat } from '@/lib/content-converter'\nimport { cn } from '@/lib/utils'\n\ninterface ContentRendererProps {\n  content: string\n  className?: string\n}\n\nexport function ContentRenderer({ content, className }: ContentRendererProps) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div \n        className={cn('content-renderer prose prose-sm max-w-none', className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  // 如果是Markdown或纯文本，使用Markdown组件\n  return (\n    <Markdown content={content} className={className} />\n  )\n}\n\n// 用于商品卡片的简化版本\nexport function ContentPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n  \n  // 提取纯文本用于预览\n  let plainText = content\n  \n  if (contentFormat === 'html') {\n    // 移除HTML标签\n    plainText = content\n      .replace(/<[^>]*>/g, '') // 移除所有HTML标签\n      .replace(/&nbsp;/g, ' ') // 替换HTML实体\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#39;/g, \"'\")\n      .replace(/\\s+/g, ' ') // 将多个空白字符替换为单个空格\n      .trim()\n  } else if (contentFormat === 'markdown') {\n    // 移除Markdown语法\n    plainText = content\n      .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n      .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n      .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n      .replace(/`(.*?)`/g, '$1') // 移除代码标记\n      .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n      .replace(/>\\s+/g, '') // 移除引用标记\n      .replace(/[-*+]\\s+/g, '') // 移除列表标记\n      .replace(/\\d+\\.\\s+/g, '') // 移除有序列表标记\n      .replace(/\\|.*?\\|/g, '') // 移除表格\n      .replace(/\\n+/g, ' ') // 将换行替换为空格\n      .trim()\n  }\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAOO,SAAS,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAwB;IAC1E,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;YAC5D,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,+BAA+B;IAC/B,qBACE,6LAAC,uIAAA,CAAA,WAAQ;QAAC,SAAS;QAAS,WAAW;;;;;;AAE3C;KArBgB;AAwBT,SAAS,eAAe,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IAClG,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,YAAY;IACZ,IAAI,YAAY;IAEhB,IAAI,kBAAkB,QAAQ;QAC5B,WAAW;QACX,YAAY,QACT,OAAO,CAAC,YAAY,IAAI,aAAa;SACrC,OAAO,CAAC,WAAW,KAAK,WAAW;SACnC,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,QAAQ,KAAK,iBAAiB;SACtC,IAAI;IACT,OAAO,IAAI,kBAAkB,YAAY;QACvC,eAAe;QACf,YAAY,QACT,OAAO,CAAC,cAAc,IAAI,SAAS;SACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;SACzC,OAAO,CAAC,cAAc,MAAM,SAAS;SACrC,OAAO,CAAC,YAAY,MAAM,SAAS;SACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;SAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;SAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;SAClC,OAAO,CAAC,aAAa,IAAI,WAAW;SACpC,OAAO,CAAC,YAAY,IAAI,OAAO;SAC/B,OAAO,CAAC,QAAQ,KAAK,WAAW;SAChC,IAAI;IACT;IAEA,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MA/CgB", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/product/%5BproductId%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { ContentRenderer } from '@/components/ui/content-renderer'\nimport { formatPrice } from '@/lib/utils'\nimport { ArrowLeft, ShoppingCart, Minus, Plus, Package, Star, Shield, Clock } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image: string\n  category: {\n    name: string\n  }\n  _count: {\n    cards: number\n  }\n}\n\nexport default function ProductPage() {\n  const params = useParams()\n  const router = useRouter()\n  const productId = Array.isArray(params.productId) ? params.productId[0] : params.productId\n\n  const [product, setProduct] = useState<Product | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [quantity, setQuantity] = useState(1)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (productId) {\n      fetchProduct()\n    }\n  }, [productId])\n\n  const fetchProduct = async () => {\n    try {\n      const response = await fetch(`/api/products/${productId}/public`)\n      if (response.ok) {\n        const data = await response.json()\n        setProduct(data)\n      } else {\n        setError('商品不存在或已下架')\n      }\n    } catch (error) {\n      setError('获取商品信息失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleQuantityChange = (newQuantity: number) => {\n    if (newQuantity >= 1 && product && newQuantity <= product._count.cards) {\n      setQuantity(newQuantity)\n    }\n  }\n\n  const handleBuyNow = () => {\n    if (product && quantity > 0) {\n      router.push(`/checkout?productId=${product.id}&quantity=${quantity}`)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-white flex items-center justify-center\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"min-h-screen bg-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Package className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">商品不存在</h3>\n          <p className=\"text-red-600 mb-6\">{error || '商品不存在或已下架'}</p>\n          <Link href=\"/\">\n            <Button>\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              返回首页\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  const totalPrice = product.price * quantity\n  const isOutOfStock = product._count.cards === 0\n  const maxQuantity = Math.min(product._count.cards, 10) // 限制最大购买数量\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              商品详情\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-6\">\n          <Link href=\"/\">\n            <Button variant=\"outline\" size=\"sm\">\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              返回商品列表\n            </Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 商品图片 */}\n          <div className=\"bg-white rounded-lg shadow-simple p-6\">\n            {product.image ? (\n              <img\n                src={product.image}\n                alt={product.name}\n                className=\"w-full h-96 object-cover rounded-lg\"\n              />\n            ) : (\n              <div className=\"w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Package className=\"w-16 h-16 text-gray-400 mx-auto mb-2\" />\n                  <span className=\"text-gray-500\">暂无图片</span>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 商品信息 */}\n          <div className=\"bg-white rounded-lg shadow-simple p-6\">\n            <div className=\"mb-4\">\n              <span className=\"text-sm text-gray-600 font-medium\">{product.category.name}</span>\n              <h1 className=\"text-2xl font-bold text-gray-900 mt-1\">{product.name}</h1>\n            </div>\n\n            {product.description && (\n              <div className=\"mb-6\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-3\">商品描述</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <ContentRenderer content={product.description} />\n                </div>\n              </div>\n            )}\n\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-900\">价格</span>\n                <span className=\"text-2xl font-bold text-gray-900\">{formatPrice(product.price)}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-900\">库存</span>\n                <span className={`text-sm font-medium ${isOutOfStock ? 'text-red-600' : 'text-green-600'}`}>\n                  {product._count.cards} 张\n                </span>\n              </div>\n            </div>\n\n            {!isOutOfStock && (\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-900 mb-2\">\n                  购买数量\n                </label>\n                <div className=\"flex items-center space-x-3\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handleQuantityChange(quantity - 1)}\n                    disabled={quantity <= 1}\n                  >\n                    <Minus className=\"w-4 h-4\" />\n                  </Button>\n                  <span className=\"text-lg font-medium w-12 text-center\">{quantity}</span>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handleQuantityChange(quantity + 1)}\n                    disabled={quantity >= maxQuantity}\n                  >\n                    <Plus className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  最多可购买 {maxQuantity} 张\n                </p>\n              </div>\n            )}\n\n            <div className=\"border-t pt-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <span className=\"text-lg font-medium text-gray-900\">总计</span>\n                <span className=\"text-2xl font-bold text-gray-900\">\n                  {formatPrice(totalPrice)}\n                </span>\n              </div>\n\n              <div className=\"space-y-3\">\n                <Button\n                  onClick={handleBuyNow}\n                  disabled={isOutOfStock}\n                  className=\"w-full\"\n                  size=\"lg\"\n                >\n                  <ShoppingCart className=\"w-5 h-5 mr-2\" />\n                  {isOutOfStock ? '暂时缺货' : '立即购买'}\n                </Button>\n\n                {!isOutOfStock && (\n                  <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-2\">购买说明</h4>\n                    <ul className=\"text-sm text-gray-600 space-y-1\">\n                      <li>• 支付成功后，卡密将立即自动发放</li>\n                      <li>• 卡密将发送到您提供的邮箱地址</li>\n                      <li>• 支持信用卡、借记卡等多种支付方式</li>\n                      <li>• 所有交易均通过 Stripe 安全处理</li>\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 商品特性 */}\n        <div className=\"mt-8 bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">服务保障</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <ShoppingCart className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-1\">即时发货</h4>\n              <p className=\"text-sm text-gray-600\">支付成功后立即自动发放卡密</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-1\">正品保证</h4>\n              <p className=\"text-sm text-gray-600\">所有卡密均为正品，可正常使用</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-1\">安全支付</h4>\n              <p className=\"text-sm text-gray-600\">采用 Stripe 安全支付系统</p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;AA0Be,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,MAAM,OAAO,CAAC,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,OAAO,SAAS;IAE1F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,OAAO,CAAC;YAChE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW;YACb,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe,KAAK,WAAW,eAAe,QAAQ,MAAM,CAAC,KAAK,EAAE;YACtE,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,WAAW,GAAG;YAC3B,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU;QACtE;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAqB,SAAS;;;;;;kCAC3C,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,MAAM,aAAa,QAAQ,KAAK,GAAG;IACnC,MAAM,eAAe,QAAQ,MAAM,CAAC,KAAK,KAAK;IAC9C,MAAM,cAAc,KAAK,GAAG,CAAC,QAAQ,MAAM,CAAC,KAAK,EAAE,IAAI,WAAW;;IAElE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,KAAK,iBACZ,6LAAC;oCACC,KAAK,QAAQ,KAAK;oCAClB,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;0CAOxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAqC,QAAQ,QAAQ,CAAC,IAAI;;;;;;0DAC1E,6LAAC;gDAAG,WAAU;0DAAyC,QAAQ,IAAI;;;;;;;;;;;;oCAGpE,QAAQ,WAAW,kBAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,kJAAA,CAAA,kBAAe;oDAAC,SAAS,QAAQ,WAAW;;;;;;;;;;;;;;;;;kDAKnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEAAoC,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;0DAE/E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,eAAe,iBAAiB,kBAAkB;;4DACvF,QAAQ,MAAM,CAAC,KAAK;4DAAC;;;;;;;;;;;;;;;;;;;oCAK3B,CAAC,8BACA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,WAAW;wDAC/C,UAAU,YAAY;kEAEtB,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;wDAAK,WAAU;kEAAwC;;;;;;kEACxD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,WAAW;wDAC/C,UAAU,YAAY;kEAEtB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGpB,6LAAC;gDAAE,WAAU;;oDAA6B;oDACjC;oDAAY;;;;;;;;;;;;;kDAKzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;0DAIjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,WAAU;wDACV,MAAK;;0EAEL,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,eAAe,SAAS;;;;;;;oDAG1B,CAAC,8BACA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;kFAAG;;;;;;kFACJ,6LAAC;kFAAG;;;;;;kFACJ,6LAAC;kFAAG;;;;;;kFACJ,6LAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;0DAE1B,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAChF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAzPwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}