import React from 'react'
import { Markdown } from './markdown'
import { detectContentFormat } from '@/lib/content-converter'
import { cn } from '@/lib/utils'

interface ContentRendererProps {
  content: string
  className?: string
}

export function ContentRenderer({ content, className }: ContentRendererProps) {
  if (!content) {
    return null
  }

  const contentFormat = detectContentFormat(content)

  // 如果是HTML内容，直接渲染
  if (contentFormat === 'html') {
    return (
      <div 
        className={cn('content-renderer prose prose-sm max-w-none', className)}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    )
  }

  // 如果是Markdown或纯文本，使用Markdown组件
  return (
    <Markdown content={content} className={className} />
  )
}

// 用于商品卡片的简化版本
export function ContentPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {
  if (!content) {
    return null
  }

  const contentFormat = detectContentFormat(content)
  
  // 提取纯文本用于预览
  let plainText = content
  
  if (contentFormat === 'html') {
    // 移除HTML标签
    plainText = content
      .replace(/<[^>]*>/g, '') // 移除所有HTML标签
      .replace(/&nbsp;/g, ' ') // 替换HTML实体
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ') // 将多个空白字符替换为单个空格
      .trim()
  } else if (contentFormat === 'markdown') {
    // 移除Markdown语法
    plainText = content
      .replace(/#{1,6}\s+/g, '') // 移除标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/`(.*?)`/g, '$1') // 移除代码标记
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
      .replace(/>\s+/g, '') // 移除引用标记
      .replace(/[-*+]\s+/g, '') // 移除列表标记
      .replace(/\d+\.\s+/g, '') // 移除有序列表标记
      .replace(/\|.*?\|/g, '') // 移除表格
      .replace(/\n+/g, ' ') // 将换行替换为空格
      .trim()
  }

  const truncated = plainText.length > maxLength 
    ? plainText.substring(0, maxLength) + '...'
    : plainText

  return (
    <p className="text-gray-600 text-sm line-clamp-2">
      {truncated}
    </p>
  )
}
