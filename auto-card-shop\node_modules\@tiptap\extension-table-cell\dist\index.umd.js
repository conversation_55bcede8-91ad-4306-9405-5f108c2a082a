(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@tiptap/core')) :
  typeof define === 'function' && define.amd ? define(['exports', '@tiptap/core'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@tiptap/extension-table-cell"] = {}, global.core));
})(this, (function (exports, core) { 'use strict';

  /**
   * This extension allows you to create table cells.
   * @see https://www.tiptap.dev/api/nodes/table-cell
   */
  const TableCell = core.Node.create({
      name: 'tableCell',
      addOptions() {
          return {
              HTMLAttributes: {},
          };
      },
      content: 'block+',
      addAttributes() {
          return {
              colspan: {
                  default: 1,
              },
              rowspan: {
                  default: 1,
              },
              colwidth: {
                  default: null,
                  parseHTML: element => {
                      const colwidth = element.getAttribute('colwidth');
                      const value = colwidth
                          ? colwidth.split(',').map(width => parseInt(width, 10))
                          : null;
                      return value;
                  },
              },
          };
      },
      tableRole: 'cell',
      isolating: true,
      parseHTML() {
          return [
              { tag: 'td' },
          ];
      },
      renderHTML({ HTMLAttributes }) {
          return ['td', core.mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
      },
  });

  exports.TableCell = TableCell;
  exports.default = TableCell;

  Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=index.umd.js.map
