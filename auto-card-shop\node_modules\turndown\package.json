{"name": "turndown", "description": "A library that converts HTML to Markdown", "version": "7.2.0", "author": "<PERSON>", "main": "lib/turndown.cjs.js", "module": "lib/turndown.es.js", "jsnext:main": "lib/turndown.es.js", "browser": {"@mixmark-io/domino": false, "./lib/turndown.cjs.js": "./lib/turndown.browser.cjs.js", "./lib/turndown.es.js": "./lib/turndown.browser.es.js", "./lib/turndown.umd.js": "./lib/turndown.browser.umd.js"}, "dependencies": {"@mixmark-io/domino": "^2.2.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "13.0.0", "@rollup/plugin-replace": "2.4.2", "browserify": "17.0.0", "rewire": "^6.0.0", "rollup": "2.52.3", "standard": "^10.0.3", "turndown-attendant": "0.0.3"}, "files": ["lib", "dist"], "keywords": ["converter", "html", "markdown"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mixmark-io/turndown.git"}, "scripts": {"build": "npm run build-cjs && npm run build-es && npm run build-umd && npm run build-iife", "build-cjs": "rollup -c config/rollup.config.cjs.js && rollup -c config/rollup.config.browser.cjs.js", "build-es": "rollup -c config/rollup.config.es.js && rollup -c config/rollup.config.browser.es.js", "build-umd": "rollup -c config/rollup.config.umd.js && rollup -c config/rollup.config.browser.umd.js", "build-iife": "rollup -c config/rollup.config.iife.js", "build-test": "browserify test/turndown-test.js --outfile test/turndown-test.browser.js", "prepare": "npm run build", "test": "npm run build && npm run build-test && standard ./src/**/*.js && node test/internals-test.js && node test/turndown-test.js"}}