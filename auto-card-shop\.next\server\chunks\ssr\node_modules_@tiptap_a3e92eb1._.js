module.exports = {

"[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// state/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/view/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// view/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/view/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$view$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/view/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/keymap/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// keymap/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/keymap/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$keymap$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/keymap/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/model/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// model/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/model/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$model$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/model/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/transform/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// transform/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/transform/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$transform$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/transform/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/commands/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// commands/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/commands/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$commands$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/commands/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/schema-list/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// schema-list/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/schema-list/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$schema$2d$list$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/schema-list/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/dropcursor/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// dropcursor/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/dropcursor/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/dropcursor/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/gapcursor/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// gapcursor/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/gapcursor/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/gapcursor/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/history/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// history/index.ts
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@tiptap/pm/history/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/history/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/pm/tables/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// tables/index.ts
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-tables/dist/index.js [app-ssr] (ecmascript)");
;
}}),
"[project]/node_modules/@tiptap/pm/tables/dist/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-tables/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/tables/dist/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@tiptap/extension-bubble-menu/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleMenu": (()=>BubbleMenu),
    "BubbleMenuPlugin": (()=>BubbleMenuPlugin),
    "BubbleMenuView": (()=>BubbleMenuView),
    "default": (()=>BubbleMenu)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tippy$2e$js$2f$dist$2f$tippy$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tippy.js/dist/tippy.esm.js [app-ssr] (ecmascript)");
;
;
;
class BubbleMenuView {
    constructor({ editor, element, view, tippyOptions = {}, updateDelay = 250, shouldShow }){
        this.preventHide = false;
        this.shouldShow = ({ view, state, from, to })=>{
            const { doc, selection } = state;
            const { empty } = selection;
            // Sometime check for `empty` is not enough.
            // Doubleclick an empty paragraph returns a node size of 2.
            // So we check also for an empty text size.
            const isEmptyTextBlock = !doc.textBetween(from, to).length && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTextSelection"])(state.selection);
            // When clicking on a element inside the bubble menu the editor "blur" event
            // is called and the bubble menu item is focussed. In this case we should
            // consider the menu as part of the editor and keep showing the menu
            const isChildOfMenu = this.element.contains(document.activeElement);
            const hasEditorFocus = view.hasFocus() || isChildOfMenu;
            if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {
                return false;
            }
            return true;
        };
        this.mousedownHandler = ()=>{
            this.preventHide = true;
        };
        this.dragstartHandler = ()=>{
            this.hide();
        };
        this.focusHandler = ()=>{
            // we use `setTimeout` to make sure `selection` is already updated
            setTimeout(()=>this.update(this.editor.view));
        };
        this.blurHandler = ({ event })=>{
            var _a;
            if (this.preventHide) {
                this.preventHide = false;
                return;
            }
            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {
                return;
            }
            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {
                return;
            }
            this.hide();
        };
        this.tippyBlurHandler = (event)=>{
            this.blurHandler({
                event
            });
        };
        this.handleDebouncedUpdate = (view, oldState)=>{
            const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));
            const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));
            if (!selectionChanged && !docChanged) {
                return;
            }
            if (this.updateDebounceTimer) {
                clearTimeout(this.updateDebounceTimer);
            }
            this.updateDebounceTimer = window.setTimeout(()=>{
                this.updateHandler(view, selectionChanged, docChanged, oldState);
            }, this.updateDelay);
        };
        this.updateHandler = (view, selectionChanged, docChanged, oldState)=>{
            var _a, _b, _c;
            const { state, composing } = view;
            const { selection } = state;
            const isSame = !selectionChanged && !docChanged;
            if (composing || isSame) {
                return;
            }
            this.createTooltip();
            // support for CellSelections
            const { ranges } = selection;
            const from = Math.min(...ranges.map((range)=>range.$from.pos));
            const to = Math.max(...ranges.map((range)=>range.$to.pos));
            const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {
                editor: this.editor,
                element: this.element,
                view,
                state,
                oldState,
                from,
                to
            });
            if (!shouldShow) {
                this.hide();
                return;
            }
            (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({
                getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect) || (()=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNodeSelection"])(state.selection)) {
                        let node = view.nodeDOM(from);
                        if (node) {
                            const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]');
                            if (nodeViewWrapper) {
                                node = nodeViewWrapper.firstChild;
                            }
                            if (node) {
                                return node.getBoundingClientRect();
                            }
                        }
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["posToDOMRect"])(view, from, to);
                })
            });
            this.show();
        };
        this.editor = editor;
        this.element = element;
        this.view = view;
        this.updateDelay = updateDelay;
        if (shouldShow) {
            this.shouldShow = shouldShow;
        }
        this.element.addEventListener('mousedown', this.mousedownHandler, {
            capture: true
        });
        this.view.dom.addEventListener('dragstart', this.dragstartHandler);
        this.editor.on('focus', this.focusHandler);
        this.editor.on('blur', this.blurHandler);
        this.tippyOptions = tippyOptions;
        // Detaches menu content from its current parent
        this.element.remove();
        this.element.style.visibility = 'visible';
    }
    createTooltip() {
        const { element: editorElement } = this.editor.options;
        const editorIsAttached = !!editorElement.parentElement;
        if (this.tippy || !editorIsAttached) {
            return;
        }
        this.tippy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tippy$2e$js$2f$dist$2f$tippy$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(editorElement, {
            duration: 0,
            getReferenceClientRect: null,
            content: this.element,
            interactive: true,
            trigger: 'manual',
            placement: 'top',
            hideOnClick: 'toggle',
            ...this.tippyOptions
        });
        // maybe we have to hide tippy on its own blur event as well
        if (this.tippy.popper.firstChild) {
            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);
        }
    }
    update(view, oldState) {
        const { state } = view;
        const hasValidSelection = state.selection.from !== state.selection.to;
        if (this.updateDelay > 0 && hasValidSelection) {
            this.handleDebouncedUpdate(view, oldState);
            return;
        }
        const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));
        const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));
        this.updateHandler(view, selectionChanged, docChanged, oldState);
    }
    show() {
        var _a;
        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();
    }
    hide() {
        var _a;
        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();
    }
    destroy() {
        var _a, _b;
        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {
            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);
        }
        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();
        this.element.removeEventListener('mousedown', this.mousedownHandler, {
            capture: true
        });
        this.view.dom.removeEventListener('dragstart', this.dragstartHandler);
        this.editor.off('focus', this.focusHandler);
        this.editor.off('blur', this.blurHandler);
    }
}
const BubbleMenuPlugin = (options)=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
        key: typeof options.pluginKey === 'string' ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"](options.pluginKey) : options.pluginKey,
        view: (view)=>new BubbleMenuView({
                view,
                ...options
            })
    });
};
/**
 * This extension allows you to create a bubble menu.
 * @see https://tiptap.dev/api/extensions/bubble-menu
 */ const BubbleMenu = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'bubbleMenu',
    addOptions () {
        return {
            element: null,
            tippyOptions: {},
            pluginKey: 'bubbleMenu',
            updateDelay: undefined,
            shouldShow: null
        };
    },
    addProseMirrorPlugins () {
        if (!this.options.element) {
            return [];
        }
        return [
            BubbleMenuPlugin({
                pluginKey: this.options.pluginKey,
                editor: this.editor,
                element: this.options.element,
                tippyOptions: this.options.tippyOptions,
                updateDelay: this.options.updateDelay,
                shouldShow: this.options.shouldShow
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-floating-menu/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FloatingMenu": (()=>FloatingMenu),
    "FloatingMenuPlugin": (()=>FloatingMenuPlugin),
    "FloatingMenuView": (()=>FloatingMenuView),
    "default": (()=>FloatingMenu)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tippy$2e$js$2f$dist$2f$tippy$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tippy.js/dist/tippy.esm.js [app-ssr] (ecmascript)");
;
;
;
class FloatingMenuView {
    getTextContent(node) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getText"])(node, {
            textSerializers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTextSerializersFromSchema"])(this.editor.schema)
        });
    }
    constructor({ editor, element, view, tippyOptions = {}, shouldShow }){
        this.preventHide = false;
        this.shouldShow = ({ view, state })=>{
            const { selection } = state;
            const { $anchor, empty } = selection;
            const isRootDepth = $anchor.depth === 1;
            const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent);
            if (!view.hasFocus() || !empty || !isRootDepth || !isEmptyTextBlock || !this.editor.isEditable) {
                return false;
            }
            return true;
        };
        this.mousedownHandler = ()=>{
            this.preventHide = true;
        };
        this.focusHandler = ()=>{
            // we use `setTimeout` to make sure `selection` is already updated
            setTimeout(()=>this.update(this.editor.view));
        };
        this.blurHandler = ({ event })=>{
            var _a;
            if (this.preventHide) {
                this.preventHide = false;
                return;
            }
            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {
                return;
            }
            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {
                return;
            }
            this.hide();
        };
        this.tippyBlurHandler = (event)=>{
            this.blurHandler({
                event
            });
        };
        this.editor = editor;
        this.element = element;
        this.view = view;
        if (shouldShow) {
            this.shouldShow = shouldShow;
        }
        this.element.addEventListener('mousedown', this.mousedownHandler, {
            capture: true
        });
        this.editor.on('focus', this.focusHandler);
        this.editor.on('blur', this.blurHandler);
        this.tippyOptions = tippyOptions;
        // Detaches menu content from its current parent
        this.element.remove();
        this.element.style.visibility = 'visible';
    }
    createTooltip() {
        const { element: editorElement } = this.editor.options;
        const editorIsAttached = !!editorElement.parentElement;
        if (this.tippy || !editorIsAttached) {
            return;
        }
        this.tippy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tippy$2e$js$2f$dist$2f$tippy$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(editorElement, {
            duration: 0,
            getReferenceClientRect: null,
            content: this.element,
            interactive: true,
            trigger: 'manual',
            placement: 'right',
            hideOnClick: 'toggle',
            ...this.tippyOptions
        });
        // maybe we have to hide tippy on its own blur event as well
        if (this.tippy.popper.firstChild) {
            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);
        }
    }
    update(view, oldState) {
        var _a, _b, _c;
        const { state } = view;
        const { doc, selection } = state;
        const { from, to } = selection;
        const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection);
        if (isSame) {
            return;
        }
        this.createTooltip();
        const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {
            editor: this.editor,
            view,
            state,
            oldState
        });
        if (!shouldShow) {
            this.hide();
            return;
        }
        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({
            getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect) || (()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["posToDOMRect"])(view, from, to))
        });
        this.show();
    }
    show() {
        var _a;
        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();
    }
    hide() {
        var _a;
        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();
    }
    destroy() {
        var _a, _b;
        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {
            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);
        }
        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();
        this.element.removeEventListener('mousedown', this.mousedownHandler, {
            capture: true
        });
        this.editor.off('focus', this.focusHandler);
        this.editor.off('blur', this.blurHandler);
    }
}
const FloatingMenuPlugin = (options)=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
        key: typeof options.pluginKey === 'string' ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"](options.pluginKey) : options.pluginKey,
        view: (view)=>new FloatingMenuView({
                view,
                ...options
            })
    });
};
/**
 * This extension allows you to create a floating menu.
 * @see https://tiptap.dev/api/extensions/floating-menu
 */ const FloatingMenu = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'floatingMenu',
    addOptions () {
        return {
            element: null,
            tippyOptions: {},
            pluginKey: 'floatingMenu',
            shouldShow: null
        };
    },
    addProseMirrorPlugins () {
        if (!this.options.element) {
            return [];
        }
        return [
            FloatingMenuPlugin({
                pluginKey: this.options.pluginKey,
                editor: this.editor,
                element: this.options.element,
                tippyOptions: this.options.tippyOptions,
                shouldShow: this.options.shouldShow
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/react/dist/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleMenu": (()=>BubbleMenu),
    "EditorConsumer": (()=>EditorConsumer),
    "EditorContent": (()=>EditorContent),
    "EditorContext": (()=>EditorContext),
    "EditorProvider": (()=>EditorProvider),
    "FloatingMenu": (()=>FloatingMenu),
    "NodeViewContent": (()=>NodeViewContent),
    "NodeViewWrapper": (()=>NodeViewWrapper),
    "PureEditorContent": (()=>PureEditorContent),
    "ReactNodeView": (()=>ReactNodeView),
    "ReactNodeViewContext": (()=>ReactNodeViewContext),
    "ReactNodeViewRenderer": (()=>ReactNodeViewRenderer),
    "ReactRenderer": (()=>ReactRenderer),
    "useCurrentEditor": (()=>useCurrentEditor),
    "useEditor": (()=>useEditor),
    "useEditorState": (()=>useEditorState),
    "useReactNodeView": (()=>useReactNodeView)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bubble$2d$menu$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-bubble-menu/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$floating$2d$menu$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-floating-menu/dist/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
function getDefaultExportFromCjs(x) {
    return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}
var shim = {
    exports: {}
};
var useSyncExternalStoreShim_production_min = {};
/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var hasRequiredUseSyncExternalStoreShim_production_min;
function requireUseSyncExternalStoreShim_production_min() {
    if (hasRequiredUseSyncExternalStoreShim_production_min) return useSyncExternalStoreShim_production_min;
    hasRequiredUseSyncExternalStoreShim_production_min = 1;
    var e = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    function h(a, b) {
        return a === b && (0 !== a || 1 / a === 1 / b) || a !== a && b !== b;
    }
    var k = "function" === typeof Object.is ? Object.is : h, l = e.useState, m = e.useEffect, n = e.useLayoutEffect, p = e.useDebugValue;
    function q(a, b) {
        var d = b(), f = l({
            inst: {
                value: d,
                getSnapshot: b
            }
        }), c = f[0].inst, g = f[1];
        n(function() {
            c.value = d;
            c.getSnapshot = b;
            r(c) && g({
                inst: c
            });
        }, [
            a,
            d,
            b
        ]);
        m(function() {
            r(c) && g({
                inst: c
            });
            return a(function() {
                r(c) && g({
                    inst: c
                });
            });
        }, [
            a
        ]);
        p(d);
        return d;
    }
    function r(a) {
        var b = a.getSnapshot;
        a = a.value;
        try {
            var d = b();
            return !k(a, d);
        } catch (f) {
            return !0;
        }
    }
    function t(a, b) {
        return b();
    }
    var u = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? t : q;
    useSyncExternalStoreShim_production_min.useSyncExternalStore = void 0 !== e.useSyncExternalStore ? e.useSyncExternalStore : u;
    return useSyncExternalStoreShim_production_min;
}
var useSyncExternalStoreShim_development = {};
/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var hasRequiredUseSyncExternalStoreShim_development;
function requireUseSyncExternalStoreShim_development() {
    if (hasRequiredUseSyncExternalStoreShim_development) return useSyncExternalStoreShim_development;
    hasRequiredUseSyncExternalStoreShim_development = 1;
    if ("TURBOPACK compile-time truthy", 1) {
        (function() {
            /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {
                __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());
            }
            var React$1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
            var ReactSharedInternals = React$1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
            function error(format) {
                {
                    {
                        for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){
                            args[_key2 - 1] = arguments[_key2];
                        }
                        printWarning('error', format, args);
                    }
                }
            }
            function printWarning(level, format, args) {
                // When changing this logic, you might want to also
                // update consoleWithStackDev.www.js as well.
                {
                    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;
                    var stack = ReactDebugCurrentFrame.getStackAddendum();
                    if (stack !== '') {
                        format += '%s';
                        args = args.concat([
                            stack
                        ]);
                    } // eslint-disable-next-line react-internal/safe-string-coercion
                    var argsWithFormat = args.map(function(item) {
                        return String(item);
                    }); // Careful: RN currently depends on this prefix
                    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it
                    // breaks IE9: https://github.com/facebook/react/issues/13610
                    // eslint-disable-next-line react-internal/no-production-logging
                    Function.prototype.apply.call(console[level], console, argsWithFormat);
                }
            }
            /**
	 * inlined Object.is polyfill to avoid requiring consumers ship their own
	 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is
	 */ function is(x, y) {
                return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare
                ;
            }
            var objectIs = typeof Object.is === 'function' ? Object.is : is;
            // dispatch for CommonJS interop named imports.
            var useState = React$1.useState, useEffect = React$1.useEffect, useLayoutEffect = React$1.useLayoutEffect, useDebugValue = React$1.useDebugValue;
            var didWarnOld18Alpha = false;
            var didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works
            // because of a very particular set of implementation details and assumptions
            // -- change any one of them and it will break. The most important assumption
            // is that updates are always synchronous, because concurrent rendering is
            // only available in versions of React that also have a built-in
            // useSyncExternalStore API. And we only use this shim when the built-in API
            // does not exist.
            //
            // Do not assume that the clever hacks used by this hook also work in general.
            // The point of this shim is to replace the need for hacks by other libraries.
            function useSyncExternalStore(subscribe, getSnapshot, // React do not expose a way to check if we're hydrating. So users of the shim
            // will need to track that themselves and return the correct value
            // from `getSnapshot`.
            getServerSnapshot) {
                {
                    if (!didWarnOld18Alpha) {
                        if (React$1.startTransition !== undefined) {
                            didWarnOld18Alpha = true;
                            error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');
                        }
                    }
                }
                // breaks the rules of React, and only works here because of specific
                // implementation details, most importantly that updates are
                // always synchronous.
                var value = getSnapshot();
                {
                    if (!didWarnUncachedGetSnapshot) {
                        var cachedValue = getSnapshot();
                        if (!objectIs(value, cachedValue)) {
                            error('The result of getSnapshot should be cached to avoid an infinite loop');
                            didWarnUncachedGetSnapshot = true;
                        }
                    }
                }
                // re-render whenever the subscribed state changes by updating an some
                // arbitrary useState hook. Then, during render, we call getSnapshot to read
                // the current value.
                //
                // Because we don't actually use the state returned by the useState hook, we
                // can save a bit of memory by storing other stuff in that slot.
                //
                // To implement the early bailout, we need to track some things on a mutable
                // object. Usually, we would put that in a useRef hook, but we can stash it in
                // our useState hook instead.
                //
                // To force a re-render, we call forceUpdate({inst}). That works because the
                // new object always fails an equality check.
                var _useState = useState({
                    inst: {
                        value: value,
                        getSnapshot: getSnapshot
                    }
                }), inst = _useState[0].inst, forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated
                // in the layout phase so we can access it during the tearing check that
                // happens on subscribe.
                useLayoutEffect(function() {
                    inst.value = value;
                    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the
                    // commit phase if there was an interleaved mutation. In concurrent mode
                    // this can happen all the time, but even in synchronous mode, an earlier
                    // effect may have mutated the store.
                    if (checkIfSnapshotChanged(inst)) {
                        // Force a re-render.
                        forceUpdate({
                            inst: inst
                        });
                    }
                }, [
                    subscribe,
                    value,
                    getSnapshot
                ]);
                useEffect(function() {
                    // Check for changes right before subscribing. Subsequent changes will be
                    // detected in the subscription handler.
                    if (checkIfSnapshotChanged(inst)) {
                        // Force a re-render.
                        forceUpdate({
                            inst: inst
                        });
                    }
                    var handleStoreChange = function() {
                        // TODO: Because there is no cross-renderer API for batching updates, it's
                        // up to the consumer of this library to wrap their subscription event
                        // with unstable_batchedUpdates. Should we try to detect when this isn't
                        // the case and print a warning in development?
                        // The store changed. Check if the snapshot changed since the last time we
                        // read from the store.
                        if (checkIfSnapshotChanged(inst)) {
                            // Force a re-render.
                            forceUpdate({
                                inst: inst
                            });
                        }
                    }; // Subscribe to the store and return a clean-up function.
                    return subscribe(handleStoreChange);
                }, [
                    subscribe
                ]);
                useDebugValue(value);
                return value;
            }
            function checkIfSnapshotChanged(inst) {
                var latestGetSnapshot = inst.getSnapshot;
                var prevValue = inst.value;
                try {
                    var nextValue = latestGetSnapshot();
                    return !objectIs(prevValue, nextValue);
                } catch (error) {
                    return true;
                }
            }
            function useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {
                // Note: The shim does not use getServerSnapshot, because pre-18 versions of
                // React do not expose a way to check if we're hydrating. So users of the shim
                // will need to track that themselves and return the correct value
                // from `getSnapshot`.
                return getSnapshot();
            }
            var canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');
            var isServerEnvironment = !canUseDOM;
            var shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;
            var useSyncExternalStore$2 = React$1.useSyncExternalStore !== undefined ? React$1.useSyncExternalStore : shim;
            useSyncExternalStoreShim_development.useSyncExternalStore = useSyncExternalStore$2;
            /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {
                __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());
            }
        })();
    }
    return useSyncExternalStoreShim_development;
}
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    shim.exports = requireUseSyncExternalStoreShim_development();
}
var shimExports = shim.exports;
const mergeRefs = (...refs)=>{
    return (node)=>{
        refs.forEach((ref)=>{
            if (typeof ref === 'function') {
                ref(node);
            } else if (ref) {
                ref.current = node;
            }
        });
    };
};
/**
 * This component renders all of the editor's node views.
 */ const Portals = ({ contentComponent })=>{
    // For performance reasons, we render the node view portals on state changes only
    const renderers = shimExports.useSyncExternalStore(contentComponent.subscribe, contentComponent.getSnapshot, contentComponent.getServerSnapshot);
    // This allows us to directly render the portals without any additional wrapper
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, Object.values(renderers));
};
function getInstance() {
    const subscribers = new Set();
    let renderers = {};
    return {
        /**
         * Subscribe to the editor instance's changes.
         */ subscribe (callback) {
            subscribers.add(callback);
            return ()=>{
                subscribers.delete(callback);
            };
        },
        getSnapshot () {
            return renderers;
        },
        getServerSnapshot () {
            return renderers;
        },
        /**
         * Adds a new NodeView Renderer to the editor.
         */ setRenderer (id, renderer) {
            renderers = {
                ...renderers,
                [id]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createPortal(renderer.reactElement, renderer.element, id)
            };
            subscribers.forEach((subscriber)=>subscriber());
        },
        /**
         * Removes a NodeView Renderer from the editor.
         */ removeRenderer (id) {
            const nextRenderers = {
                ...renderers
            };
            delete nextRenderers[id];
            renderers = nextRenderers;
            subscribers.forEach((subscriber)=>subscriber());
        }
    };
}
class PureEditorContent extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Component {
    constructor(props){
        var _a;
        super(props);
        this.editorContentRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createRef();
        this.initialized = false;
        this.state = {
            hasContentComponentInitialized: Boolean((_a = props.editor) === null || _a === void 0 ? void 0 : _a.contentComponent)
        };
    }
    componentDidMount() {
        this.init();
    }
    componentDidUpdate() {
        this.init();
    }
    init() {
        const editor = this.props.editor;
        if (editor && !editor.isDestroyed && editor.options.element) {
            if (editor.contentComponent) {
                return;
            }
            const element = this.editorContentRef.current;
            element.append(...editor.options.element.childNodes);
            editor.setOptions({
                element
            });
            editor.contentComponent = getInstance();
            // Has the content component been initialized?
            if (!this.state.hasContentComponentInitialized) {
                // Subscribe to the content component
                this.unsubscribeToContentComponent = editor.contentComponent.subscribe(()=>{
                    this.setState((prevState)=>{
                        if (!prevState.hasContentComponentInitialized) {
                            return {
                                hasContentComponentInitialized: true
                            };
                        }
                        return prevState;
                    });
                    // Unsubscribe to previous content component
                    if (this.unsubscribeToContentComponent) {
                        this.unsubscribeToContentComponent();
                    }
                });
            }
            editor.createNodeViews();
            this.initialized = true;
        }
    }
    componentWillUnmount() {
        const editor = this.props.editor;
        if (!editor) {
            return;
        }
        this.initialized = false;
        if (!editor.isDestroyed) {
            editor.view.setProps({
                nodeViews: {}
            });
        }
        if (this.unsubscribeToContentComponent) {
            this.unsubscribeToContentComponent();
        }
        editor.contentComponent = null;
        if (!editor.options.element.firstChild) {
            return;
        }
        const newElement = document.createElement('div');
        newElement.append(...editor.options.element.childNodes);
        editor.setOptions({
            element: newElement
        });
    }
    render() {
        const { editor, innerRef, ...rest } = this.props;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
            ref: mergeRefs(innerRef, this.editorContentRef),
            ...rest
        }), (editor === null || editor === void 0 ? void 0 : editor.contentComponent) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Portals, {
            contentComponent: editor.contentComponent
        }));
    }
}
// EditorContent should be re-created whenever the Editor instance changes
const EditorContentWithKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const key = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>{
        return Math.floor(Math.random() * 0xffffffff).toString();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        props.editor
    ]);
    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(PureEditorContent, {
        key,
        innerRef: ref,
        ...props
    });
});
const EditorContent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(EditorContentWithKey);
var react = function equal(a, b) {
    if (a === b) return true;
    if (a && b && typeof a == 'object' && typeof b == 'object') {
        if (a.constructor !== b.constructor) return false;
        var length, i, keys;
        if (Array.isArray(a)) {
            length = a.length;
            if (length != b.length) return false;
            for(i = length; i-- !== 0;)if (!equal(a[i], b[i])) return false;
            return true;
        }
        if (a instanceof Map && b instanceof Map) {
            if (a.size !== b.size) return false;
            for (i of a.entries())if (!b.has(i[0])) return false;
            for (i of a.entries())if (!equal(i[1], b.get(i[0]))) return false;
            return true;
        }
        if (a instanceof Set && b instanceof Set) {
            if (a.size !== b.size) return false;
            for (i of a.entries())if (!b.has(i[0])) return false;
            return true;
        }
        if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
            length = a.length;
            if (length != b.length) return false;
            for(i = length; i-- !== 0;)if (a[i] !== b[i]) return false;
            return true;
        }
        if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
        if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();
        if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();
        keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length) return false;
        for(i = length; i-- !== 0;)if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
        for(i = length; i-- !== 0;){
            var key = keys[i];
            if (key === '_owner' && a.$$typeof) {
                continue;
            }
            if (!equal(a[key], b[key])) return false;
        }
        return true;
    }
    // true if both NaN, false otherwise
    return a !== a && b !== b;
};
var deepEqual = /*@__PURE__*/ getDefaultExportFromCjs(react);
var withSelector = {
    exports: {}
};
var withSelector_production_min = {};
/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var hasRequiredWithSelector_production_min;
function requireWithSelector_production_min() {
    if (hasRequiredWithSelector_production_min) return withSelector_production_min;
    hasRequiredWithSelector_production_min = 1;
    var h = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], n = shimExports;
    function p(a, b) {
        return a === b && (0 !== a || 1 / a === 1 / b) || a !== a && b !== b;
    }
    var q = "function" === typeof Object.is ? Object.is : p, r = n.useSyncExternalStore, t = h.useRef, u = h.useEffect, v = h.useMemo, w = h.useDebugValue;
    withSelector_production_min.useSyncExternalStoreWithSelector = function(a, b, e, l, g) {
        var c = t(null);
        if (null === c.current) {
            var f = {
                hasValue: !1,
                value: null
            };
            c.current = f;
        } else f = c.current;
        c = v(function() {
            function a(a) {
                if (!c) {
                    c = !0;
                    d = a;
                    a = l(a);
                    if (void 0 !== g && f.hasValue) {
                        var b = f.value;
                        if (g(b, a)) return k = b;
                    }
                    return k = a;
                }
                b = k;
                if (q(d, a)) return b;
                var e = l(a);
                if (void 0 !== g && g(b, e)) return b;
                d = a;
                return k = e;
            }
            var c = !1, d, k, m = void 0 === e ? null : e;
            return [
                function() {
                    return a(b());
                },
                null === m ? void 0 : function() {
                    return a(m());
                }
            ];
        }, [
            b,
            e,
            l,
            g
        ]);
        var d = r(a, c[0], c[1]);
        u(function() {
            f.hasValue = !0;
            f.value = d;
        }, [
            d
        ]);
        w(d);
        return d;
    };
    return withSelector_production_min;
}
var withSelector_development = {};
/**
 * @license React
 * use-sync-external-store-shim/with-selector.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var hasRequiredWithSelector_development;
function requireWithSelector_development() {
    if (hasRequiredWithSelector_development) return withSelector_development;
    hasRequiredWithSelector_development = 1;
    if ("TURBOPACK compile-time truthy", 1) {
        (function() {
            /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {
                __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());
            }
            var React$1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
            var shim = shimExports;
            /**
	 * inlined Object.is polyfill to avoid requiring consumers ship their own
	 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is
	 */ function is(x, y) {
                return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare
                ;
            }
            var objectIs = typeof Object.is === 'function' ? Object.is : is;
            var useSyncExternalStore = shim.useSyncExternalStore;
            // for CommonJS interop.
            var useRef = React$1.useRef, useEffect = React$1.useEffect, useMemo = React$1.useMemo, useDebugValue = React$1.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.
            function useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {
                // Use this to track the rendered snapshot.
                var instRef = useRef(null);
                var inst;
                if (instRef.current === null) {
                    inst = {
                        hasValue: false,
                        value: null
                    };
                    instRef.current = inst;
                } else {
                    inst = instRef.current;
                }
                var _useMemo = useMemo(function() {
                    // Track the memoized state using closure variables that are local to this
                    // memoized instance of a getSnapshot function. Intentionally not using a
                    // useRef hook, because that state would be shared across all concurrent
                    // copies of the hook/component.
                    var hasMemo = false;
                    var memoizedSnapshot;
                    var memoizedSelection;
                    var memoizedSelector = function(nextSnapshot) {
                        if (!hasMemo) {
                            // The first time the hook is called, there is no memoized result.
                            hasMemo = true;
                            memoizedSnapshot = nextSnapshot;
                            var _nextSelection = selector(nextSnapshot);
                            if (isEqual !== undefined) {
                                // Even if the selector has changed, the currently rendered selection
                                // may be equal to the new selection. We should attempt to reuse the
                                // current value if possible, to preserve downstream memoizations.
                                if (inst.hasValue) {
                                    var currentSelection = inst.value;
                                    if (isEqual(currentSelection, _nextSelection)) {
                                        memoizedSelection = currentSelection;
                                        return currentSelection;
                                    }
                                }
                            }
                            memoizedSelection = _nextSelection;
                            return _nextSelection;
                        } // We may be able to reuse the previous invocation's result.
                        // We may be able to reuse the previous invocation's result.
                        var prevSnapshot = memoizedSnapshot;
                        var prevSelection = memoizedSelection;
                        if (objectIs(prevSnapshot, nextSnapshot)) {
                            // The snapshot is the same as last time. Reuse the previous selection.
                            return prevSelection;
                        } // The snapshot has changed, so we need to compute a new selection.
                        // The snapshot has changed, so we need to compute a new selection.
                        var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data
                        // has changed. If it hasn't, return the previous selection. That signals
                        // to React that the selections are conceptually equal, and we can bail
                        // out of rendering.
                        // If a custom isEqual function is provided, use that to check if the data
                        // has changed. If it hasn't, return the previous selection. That signals
                        // to React that the selections are conceptually equal, and we can bail
                        // out of rendering.
                        if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {
                            return prevSelection;
                        }
                        memoizedSnapshot = nextSnapshot;
                        memoizedSelection = nextSelection;
                        return nextSelection;
                    }; // Assigning this to a constant so that Flow knows it can't change.
                    // Assigning this to a constant so that Flow knows it can't change.
                    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;
                    var getSnapshotWithSelector = function() {
                        return memoizedSelector(getSnapshot());
                    };
                    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function() {
                        return memoizedSelector(maybeGetServerSnapshot());
                    };
                    return [
                        getSnapshotWithSelector,
                        getServerSnapshotWithSelector
                    ];
                }, [
                    getSnapshot,
                    getServerSnapshot,
                    selector,
                    isEqual
                ]), getSelection = _useMemo[0], getServerSelection = _useMemo[1];
                var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);
                useEffect(function() {
                    inst.hasValue = true;
                    inst.value = value;
                }, [
                    value
                ]);
                useDebugValue(value);
                return value;
            }
            withSelector_development.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;
            /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {
                __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());
            }
        })();
    }
    return withSelector_development;
}
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    withSelector.exports = requireWithSelector_development();
}
var withSelectorExports = withSelector.exports;
const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"];
/**
 * To synchronize the editor instance with the component state,
 * we need to create a separate instance that is not affected by the component re-renders.
 */ class EditorStateManager {
    constructor(initialEditor){
        this.transactionNumber = 0;
        this.lastTransactionNumber = 0;
        this.subscribers = new Set();
        this.editor = initialEditor;
        this.lastSnapshot = {
            editor: initialEditor,
            transactionNumber: 0
        };
        this.getSnapshot = this.getSnapshot.bind(this);
        this.getServerSnapshot = this.getServerSnapshot.bind(this);
        this.watch = this.watch.bind(this);
        this.subscribe = this.subscribe.bind(this);
    }
    /**
     * Get the current editor instance.
     */ getSnapshot() {
        if (this.transactionNumber === this.lastTransactionNumber) {
            return this.lastSnapshot;
        }
        this.lastTransactionNumber = this.transactionNumber;
        this.lastSnapshot = {
            editor: this.editor,
            transactionNumber: this.transactionNumber
        };
        return this.lastSnapshot;
    }
    /**
     * Always disable the editor on the server-side.
     */ getServerSnapshot() {
        return {
            editor: null,
            transactionNumber: 0
        };
    }
    /**
     * Subscribe to the editor instance's changes.
     */ subscribe(callback) {
        this.subscribers.add(callback);
        return ()=>{
            this.subscribers.delete(callback);
        };
    }
    /**
     * Watch the editor instance for changes.
     */ watch(nextEditor) {
        this.editor = nextEditor;
        if (this.editor) {
            /**
             * This will force a re-render when the editor state changes.
             * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.
             * This could be more efficient, but it's a good trade-off for now.
             */ const fn = ()=>{
                this.transactionNumber += 1;
                this.subscribers.forEach((callback)=>callback());
            };
            const currentEditor = this.editor;
            currentEditor.on('transaction', fn);
            return ()=>{
                currentEditor.off('transaction', fn);
            };
        }
        return undefined;
    }
}
/**
 * This hook allows you to watch for changes on the editor instance.
 * It will allow you to select a part of the editor state and re-render the component when it changes.
 * @example
 * ```tsx
 * const editor = useEditor({...options})
 * const { currentSelection } = useEditorState({
 *  editor,
 *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),
 * })
 */ function useEditorState(options) {
    var _a;
    const [editorStateManager] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new EditorStateManager(options.editor));
    // Using the `useSyncExternalStore` hook to sync the editor instance with the component state
    const selectedState = withSelectorExports.useSyncExternalStoreWithSelector(editorStateManager.subscribe, editorStateManager.getSnapshot, editorStateManager.getServerSnapshot, options.selector, (_a = options.equalityFn) !== null && _a !== void 0 ? _a : deepEqual);
    useIsomorphicLayoutEffect(()=>{
        return editorStateManager.watch(options.editor);
    }, [
        options.editor,
        editorStateManager
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDebugValue"])(selectedState);
    return selectedState;
}
const isDev = ("TURBOPACK compile-time value", "development") !== 'production';
const isSSR = typeof window === 'undefined';
const isNext = isSSR || Boolean(typeof window !== 'undefined' && window.next);
/**
 * This class handles the creation, destruction, and re-creation of the editor instance.
 */ class EditorInstanceManager {
    constructor(options){
        /**
         * The current editor instance.
         */ this.editor = null;
        /**
         * The subscriptions to notify when the editor instance
         * has been created or destroyed.
         */ this.subscriptions = new Set();
        /**
         * Whether the editor has been mounted.
         */ this.isComponentMounted = false;
        /**
         * The most recent dependencies array.
         */ this.previousDeps = null;
        /**
         * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.
         */ this.instanceId = '';
        this.options = options;
        this.subscriptions = new Set();
        this.setEditor(this.getInitialEditor());
        this.scheduleDestroy();
        this.getEditor = this.getEditor.bind(this);
        this.getServerSnapshot = this.getServerSnapshot.bind(this);
        this.subscribe = this.subscribe.bind(this);
        this.refreshEditorInstance = this.refreshEditorInstance.bind(this);
        this.scheduleDestroy = this.scheduleDestroy.bind(this);
        this.onRender = this.onRender.bind(this);
        this.createEditor = this.createEditor.bind(this);
    }
    setEditor(editor) {
        this.editor = editor;
        this.instanceId = Math.random().toString(36).slice(2, 9);
        // Notify all subscribers that the editor instance has been created
        this.subscriptions.forEach((cb)=>cb());
    }
    getInitialEditor() {
        if (this.options.current.immediatelyRender === undefined) {
            if (isSSR || isNext) {
                // TODO in the next major release, we should throw an error here
                if ("TURBOPACK compile-time truthy", 1) {
                    /**
                     * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd
                     * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.
                     */ console.warn('Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.');
                }
                // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production
                return null;
            }
            // Default to immediately rendering when client-side rendering
            return this.createEditor();
        }
        if (this.options.current.immediatelyRender && isSSR && isDev) {
            // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.
            throw new Error('Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.');
        }
        if (this.options.current.immediatelyRender) {
            return this.createEditor();
        }
        return null;
    }
    /**
     * Create a new editor instance. And attach event listeners.
     */ createEditor() {
        const optionsToApply = {
            ...this.options.current,
            // Always call the most recent version of the callback function by default
            onBeforeCreate: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onBeforeCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onBlur: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onBlur) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onCreate: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onDestroy: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onDestroy) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onFocus: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onFocus) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onSelectionUpdate: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onSelectionUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onTransaction: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onTransaction) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onUpdate: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onContentError: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onContentError) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onDrop: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            },
            onPaste: (...args)=>{
                var _a, _b;
                return (_b = (_a = this.options.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
            }
        };
        const editor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Editor"](optionsToApply);
        // no need to keep track of the event listeners, they will be removed when the editor is destroyed
        return editor;
    }
    /**
     * Get the current editor instance.
     */ getEditor() {
        return this.editor;
    }
    /**
     * Always disable the editor on the server-side.
     */ getServerSnapshot() {
        return null;
    }
    /**
     * Subscribe to the editor instance's changes.
     */ subscribe(onStoreChange) {
        this.subscriptions.add(onStoreChange);
        return ()=>{
            this.subscriptions.delete(onStoreChange);
        };
    }
    static compareOptions(a, b) {
        return Object.keys(a).every((key)=>{
            if ([
                'onCreate',
                'onBeforeCreate',
                'onDestroy',
                'onUpdate',
                'onTransaction',
                'onFocus',
                'onBlur',
                'onSelectionUpdate',
                'onContentError',
                'onDrop',
                'onPaste'
            ].includes(key)) {
                // we don't want to compare callbacks, they are always different and only registered once
                return true;
            }
            // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here
            if (key === 'extensions' && a.extensions && b.extensions) {
                if (a.extensions.length !== b.extensions.length) {
                    return false;
                }
                return a.extensions.every((extension, index)=>{
                    var _a;
                    if (extension !== ((_a = b.extensions) === null || _a === void 0 ? void 0 : _a[index])) {
                        return false;
                    }
                    return true;
                });
            }
            if (a[key] !== b[key]) {
                // if any of the options have changed, we should update the editor options
                return false;
            }
            return true;
        });
    }
    /**
     * On each render, we will create, update, or destroy the editor instance.
     * @param deps The dependencies to watch for changes
     * @returns A cleanup function
     */ onRender(deps) {
        // The returned callback will run on each render
        return ()=>{
            this.isComponentMounted = true;
            // Cleanup any scheduled destructions, since we are currently rendering
            clearTimeout(this.scheduledDestructionTimeout);
            if (this.editor && !this.editor.isDestroyed && deps.length === 0) {
                // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally
                if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {
                    // But, the options are different, so we need to update the editor options
                    // Still, this is faster than re-creating the editor
                    this.editor.setOptions({
                        ...this.options.current,
                        editable: this.editor.isEditable
                    });
                }
            } else {
                // When the editor:
                // - does not yet exist
                // - is destroyed
                // - the deps array changes
                // We need to destroy the editor instance and re-initialize it
                this.refreshEditorInstance(deps);
            }
            return ()=>{
                this.isComponentMounted = false;
                this.scheduleDestroy();
            };
        };
    }
    /**
     * Recreate the editor instance if the dependencies have changed.
     */ refreshEditorInstance(deps) {
        if (this.editor && !this.editor.isDestroyed) {
            // Editor instance already exists
            if (this.previousDeps === null) {
                // If lastDeps has not yet been initialized, reuse the current editor instance
                this.previousDeps = deps;
                return;
            }
            const depsAreEqual = this.previousDeps.length === deps.length && this.previousDeps.every((dep, index)=>dep === deps[index]);
            if (depsAreEqual) {
                // deps exist and are equal, no need to recreate
                return;
            }
        }
        if (this.editor && !this.editor.isDestroyed) {
            // Destroy the editor instance if it exists
            this.editor.destroy();
        }
        this.setEditor(this.createEditor());
        // Update the lastDeps to the current deps
        this.previousDeps = deps;
    }
    /**
     * Schedule the destruction of the editor instance.
     * This will only destroy the editor if it was not mounted on the next tick.
     * This is to avoid destroying the editor instance when it's actually still mounted.
     */ scheduleDestroy() {
        const currentInstanceId = this.instanceId;
        const currentEditor = this.editor;
        // Wait two ticks to see if the component is still mounted
        this.scheduledDestructionTimeout = setTimeout(()=>{
            if (this.isComponentMounted && this.instanceId === currentInstanceId) {
                // If still mounted on the following tick, with the same instanceId, do not destroy the editor
                if (currentEditor) {
                    // just re-apply options as they might have changed
                    currentEditor.setOptions(this.options.current);
                }
                return;
            }
            if (currentEditor && !currentEditor.isDestroyed) {
                currentEditor.destroy();
                if (this.instanceId === currentInstanceId) {
                    this.setEditor(null);
                }
            }
        // This allows the effect to run again between ticks
        // which may save us from having to re-create the editor
        }, 1);
    }
}
function useEditor(options = {}, deps = []) {
    const mostRecentOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(options);
    mostRecentOptions.current = options;
    const [instanceManager] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new EditorInstanceManager(mostRecentOptions));
    const editor = shimExports.useSyncExternalStore(instanceManager.subscribe, instanceManager.getEditor, instanceManager.getServerSnapshot);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDebugValue"])(editor);
    // This effect will handle creating/updating the editor instance
    // eslint-disable-next-line react-hooks/exhaustive-deps
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(instanceManager.onRender(deps));
    // The default behavior is to re-render on each transaction
    // This is legacy behavior that will be removed in future versions
    useEditorState({
        editor,
        selector: ({ transactionNumber })=>{
            if (options.shouldRerenderOnTransaction === false) {
                // This will prevent the editor from re-rendering on each transaction
                return null;
            }
            // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`
            if (options.immediatelyRender && transactionNumber === 0) {
                return 0;
            }
            return transactionNumber + 1;
        }
    });
    return editor;
}
const EditorContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({
    editor: null
});
const EditorConsumer = EditorContext.Consumer;
/**
 * A hook to get the current editor instance.
 */ const useCurrentEditor = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(EditorContext);
/**
 * This is the provider component for the editor.
 * It allows the editor to be accessible across the entire component tree
 * with `useCurrentEditor`.
 */ function EditorProvider({ children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions }) {
    const editor = useEditor(editorOptions);
    if (!editor) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EditorContext.Provider, {
        value: {
            editor
        }
    }, slotBefore, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EditorConsumer, null, ({ editor: currentEditor })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EditorContent, {
            editor: currentEditor,
            ...editorContainerProps
        })), children, slotAfter);
}
const BubbleMenu = (props)=>{
    const [element, setElement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const { editor: currentEditor } = useCurrentEditor();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        var _a;
        if (!element) {
            return;
        }
        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {
            return;
        }
        const { pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null } = props;
        const menuEditor = editor || currentEditor;
        if (!menuEditor) {
            console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.');
            return;
        }
        const plugin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bubble$2d$menu$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BubbleMenuPlugin"])({
            updateDelay,
            editor: menuEditor,
            element,
            pluginKey,
            shouldShow,
            tippyOptions
        });
        menuEditor.registerPlugin(plugin);
        return ()=>{
            menuEditor.unregisterPlugin(pluginKey);
        };
    }, [
        props.editor,
        currentEditor,
        element
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: setElement,
        className: props.className,
        style: {
            visibility: 'hidden'
        }
    }, props.children);
};
const FloatingMenu = (props)=>{
    const [element, setElement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const { editor: currentEditor } = useCurrentEditor();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        var _a;
        if (!element) {
            return;
        }
        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {
            return;
        }
        const { pluginKey = 'floatingMenu', editor, tippyOptions = {}, shouldShow = null } = props;
        const menuEditor = editor || currentEditor;
        if (!menuEditor) {
            console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.');
            return;
        }
        const plugin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$floating$2d$menu$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloatingMenuPlugin"])({
            pluginKey,
            editor: menuEditor,
            element,
            tippyOptions,
            shouldShow
        });
        menuEditor.registerPlugin(plugin);
        return ()=>{
            menuEditor.unregisterPlugin(pluginKey);
        };
    }, [
        props.editor,
        currentEditor,
        element
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: setElement,
        className: props.className,
        style: {
            visibility: 'hidden'
        }
    }, props.children);
};
const ReactNodeViewContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({
    onDragStart: undefined
});
const useReactNodeView = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ReactNodeViewContext);
const NodeViewContent = (props)=>{
    const Tag = props.as || 'div';
    const { nodeViewContentRef } = useReactNodeView();
    return(// @ts-ignore
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Tag, {
        ...props,
        ref: nodeViewContentRef,
        "data-node-view-content": "",
        style: {
            whiteSpace: 'pre-wrap',
            ...props.style
        }
    }));
};
const NodeViewWrapper = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef((props, ref)=>{
    const { onDragStart } = useReactNodeView();
    const Tag = props.as || 'div';
    return(// @ts-ignore
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Tag, {
        ...props,
        ref: ref,
        "data-node-view-wrapper": "",
        onDragStart: onDragStart,
        style: {
            whiteSpace: 'normal',
            ...props.style
        }
    }));
});
/**
 * Check if a component is a class component.
 * @param Component
 * @returns {boolean}
 */ function isClassComponent(Component) {
    return !!(typeof Component === 'function' && Component.prototype && Component.prototype.isReactComponent);
}
/**
 * Check if a component is a forward ref component.
 * @param Component
 * @returns {boolean}
 */ function isForwardRefComponent(Component) {
    return !!(typeof Component === 'object' && Component.$$typeof && (Component.$$typeof.toString() === 'Symbol(react.forward_ref)' || Component.$$typeof.description === 'react.forward_ref'));
}
/**
 * Check if a component is a memoized component.
 * @param Component
 * @returns {boolean}
 */ function isMemoComponent(Component) {
    return !!(typeof Component === 'object' && Component.$$typeof && (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo'));
}
/**
 * Check if a component can safely receive a ref prop.
 * This includes class components, forwardRef components, and memoized components
 * that wrap forwardRef or class components.
 * @param Component
 * @returns {boolean}
 */ function canReceiveRef(Component) {
    // Check if it's a class component
    if (isClassComponent(Component)) {
        return true;
    }
    // Check if it's a forwardRef component
    if (isForwardRefComponent(Component)) {
        return true;
    }
    // Check if it's a memoized component
    if (isMemoComponent(Component)) {
        // For memoized components, check the wrapped component
        const wrappedComponent = Component.type;
        if (wrappedComponent) {
            return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent);
        }
    }
    return false;
}
/**
 * Check if we're running React 19+ by detecting if function components support ref props
 * @returns {boolean}
 */ function isReact19Plus() {
    // React 19 is detected by checking React version if available
    // In practice, we'll use a more conservative approach and assume React 18 behavior
    // unless we can definitively detect React 19
    try {
        // @ts-ignore
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]) {
            const majorVersion = parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"].split('.')[0], 10);
            return majorVersion >= 19;
        }
    } catch  {
    // Fallback to React 18 behavior if we can't determine version
    }
    return false;
}
/**
 * The ReactRenderer class. It's responsible for rendering React components inside the editor.
 * @example
 * new ReactRenderer(MyComponent, {
 *   editor,
 *   props: {
 *     foo: 'bar',
 *   },
 *   as: 'span',
 * })
*/ class ReactRenderer {
    /**
     * Immediately creates element and renders the provided React component.
     */ constructor(component, { editor, props = {}, as = 'div', className = '' }){
        this.ref = null;
        this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString();
        this.component = component;
        this.editor = editor;
        this.props = props;
        this.element = document.createElement(as);
        this.element.classList.add('react-renderer');
        if (className) {
            this.element.classList.add(...className.split(' '));
        }
        if (this.editor.isInitialized) {
            // On first render, we need to flush the render synchronously
            // Renders afterwards can be async, but this fixes a cursor positioning issue
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["flushSync"])(()=>{
                this.render();
            });
        } else {
            this.render();
        }
    }
    /**
     * Render the React component.
     */ render() {
        var _a;
        const Component = this.component;
        const props = this.props;
        const editor = this.editor;
        // Handle ref forwarding with React 18/19 compatibility
        const isReact19 = isReact19Plus();
        const componentCanReceiveRef = canReceiveRef(Component);
        const elementProps = {
            ...props
        };
        // Always remove ref if the component cannot receive it (unless React 19+)
        if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {
            delete elementProps.ref;
        }
        // Only assign our own ref if allowed
        if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {
            // @ts-ignore - Setting ref prop for compatible components
            elementProps.ref = (ref)=>{
                this.ref = ref;
            };
        }
        this.reactElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Component, {
            ...elementProps
        });
        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.setRenderer(this.id, this);
    }
    /**
     * Re-renders the React component with new props.
     */ updateProps(props = {}) {
        this.props = {
            ...this.props,
            ...props
        };
        this.render();
    }
    /**
     * Destroy the React component.
     */ destroy() {
        var _a;
        const editor = this.editor;
        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.removeRenderer(this.id);
    }
    /**
     * Update the attributes of the element that holds the React component.
     */ updateAttributes(attributes) {
        Object.keys(attributes).forEach((key)=>{
            this.element.setAttribute(key, attributes[key]);
        });
    }
}
class ReactNodeView extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NodeView"] {
    /**
     * Setup the React component.
     * Called on initialization.
     */ mount() {
        const props = {
            editor: this.editor,
            node: this.node,
            decorations: this.decorations,
            innerDecorations: this.innerDecorations,
            view: this.view,
            selected: false,
            extension: this.extension,
            HTMLAttributes: this.HTMLAttributes,
            getPos: ()=>this.getPos(),
            updateAttributes: (attributes = {})=>this.updateAttributes(attributes),
            deleteNode: ()=>this.deleteNode(),
            ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createRef"])()
        };
        if (!this.component.displayName) {
            const capitalizeFirstChar = (string)=>{
                return string.charAt(0).toUpperCase() + string.substring(1);
            };
            this.component.displayName = capitalizeFirstChar(this.extension.name);
        }
        const onDragStart = this.onDragStart.bind(this);
        const nodeViewContentRef = (element)=>{
            if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {
                element.appendChild(this.contentDOMElement);
            }
        };
        const context = {
            onDragStart,
            nodeViewContentRef
        };
        const Component = this.component;
        // For performance reasons, we memoize the provider component
        // And all of the things it requires are declared outside of the component, so it doesn't need to re-render
        const ReactNodeViewProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])((componentProps)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(ReactNodeViewContext.Provider, {
                value: context
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Component, componentProps));
        });
        ReactNodeViewProvider.displayName = 'ReactNodeView';
        if (this.node.isLeaf) {
            this.contentDOMElement = null;
        } else if (this.options.contentDOMElementTag) {
            this.contentDOMElement = document.createElement(this.options.contentDOMElementTag);
        } else {
            this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div');
        }
        if (this.contentDOMElement) {
            this.contentDOMElement.dataset.nodeViewContentReact = '';
            // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari
            // With this fix it seems to work fine
            // See: https://github.com/ueberdosis/tiptap/issues/1197
            this.contentDOMElement.style.whiteSpace = 'inherit';
        }
        let as = this.node.isInline ? 'span' : 'div';
        if (this.options.as) {
            as = this.options.as;
        }
        const { className = '' } = this.options;
        this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this);
        this.renderer = new ReactRenderer(ReactNodeViewProvider, {
            editor: this.editor,
            props,
            as,
            className: `node-${this.node.type.name} ${className}`.trim()
        });
        this.editor.on('selectionUpdate', this.handleSelectionUpdate);
        this.updateElementAttributes();
    }
    /**
     * Return the DOM element.
     * This is the element that will be used to display the node view.
     */ get dom() {
        var _a;
        if (this.renderer.element.firstElementChild && !((_a = this.renderer.element.firstElementChild) === null || _a === void 0 ? void 0 : _a.hasAttribute('data-node-view-wrapper'))) {
            throw Error('Please use the NodeViewWrapper component for your node view.');
        }
        return this.renderer.element;
    }
    /**
     * Return the content DOM element.
     * This is the element that will be used to display the rich-text content of the node.
     */ get contentDOM() {
        if (this.node.isLeaf) {
            return null;
        }
        return this.contentDOMElement;
    }
    /**
     * On editor selection update, check if the node is selected.
     * If it is, call `selectNode`, otherwise call `deselectNode`.
     */ handleSelectionUpdate() {
        const { from, to } = this.editor.state.selection;
        const pos = this.getPos();
        if (typeof pos !== 'number') {
            return;
        }
        if (from <= pos && to >= pos + this.node.nodeSize) {
            if (this.renderer.props.selected) {
                return;
            }
            this.selectNode();
        } else {
            if (!this.renderer.props.selected) {
                return;
            }
            this.deselectNode();
        }
    }
    /**
     * On update, update the React component.
     * To prevent unnecessary updates, the `update` option can be used.
     */ update(node, decorations, innerDecorations) {
        const rerenderComponent = (props)=>{
            this.renderer.updateProps(props);
            if (typeof this.options.attrs === 'function') {
                this.updateElementAttributes();
            }
        };
        if (node.type !== this.node.type) {
            return false;
        }
        if (typeof this.options.update === 'function') {
            const oldNode = this.node;
            const oldDecorations = this.decorations;
            const oldInnerDecorations = this.innerDecorations;
            this.node = node;
            this.decorations = decorations;
            this.innerDecorations = innerDecorations;
            return this.options.update({
                oldNode,
                oldDecorations,
                newNode: node,
                newDecorations: decorations,
                oldInnerDecorations,
                innerDecorations,
                updateProps: ()=>rerenderComponent({
                        node,
                        decorations,
                        innerDecorations
                    })
            });
        }
        if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {
            return true;
        }
        this.node = node;
        this.decorations = decorations;
        this.innerDecorations = innerDecorations;
        rerenderComponent({
            node,
            decorations,
            innerDecorations
        });
        return true;
    }
    /**
     * Select the node.
     * Add the `selected` prop and the `ProseMirror-selectednode` class.
     */ selectNode() {
        this.renderer.updateProps({
            selected: true
        });
        this.renderer.element.classList.add('ProseMirror-selectednode');
    }
    /**
     * Deselect the node.
     * Remove the `selected` prop and the `ProseMirror-selectednode` class.
     */ deselectNode() {
        this.renderer.updateProps({
            selected: false
        });
        this.renderer.element.classList.remove('ProseMirror-selectednode');
    }
    /**
     * Destroy the React component instance.
     */ destroy() {
        this.renderer.destroy();
        this.editor.off('selectionUpdate', this.handleSelectionUpdate);
        this.contentDOMElement = null;
    }
    /**
     * Update the attributes of the top-level element that holds the React component.
     * Applying the attributes defined in the `attrs` option.
     */ updateElementAttributes() {
        if (this.options.attrs) {
            let attrsObj = {};
            if (typeof this.options.attrs === 'function') {
                const extensionAttributes = this.editor.extensionManager.attributes;
                const HTMLAttributes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRenderedAttributes"])(this.node, extensionAttributes);
                attrsObj = this.options.attrs({
                    node: this.node,
                    HTMLAttributes
                });
            } else {
                attrsObj = this.options.attrs;
            }
            this.renderer.updateAttributes(attrsObj);
        }
    }
}
/**
 * Create a React node view renderer.
 */ function ReactNodeViewRenderer(component, options) {
    return (props)=>{
        // try to get the parent component
        // this is important for vue devtools to show the component hierarchy correctly
        // maybe it’s `undefined` because <editor-content> isn’t rendered yet
        if (!props.editor.contentComponent) {
            return {};
        }
        return new ReactNodeView(component, props, options);
    };
}
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-blockquote/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Blockquote": (()=>Blockquote),
    "default": (()=>Blockquote),
    "inputRegex": (()=>inputRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Matches a blockquote to a `>` as input.
 */ const inputRegex = /^\s*>\s$/;
/**
 * This extension allows you to create blockquotes.
 * @see https://tiptap.dev/api/nodes/blockquote
 */ const Blockquote = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'blockquote',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    content: 'block+',
    group: 'block',
    defining: true,
    parseHTML () {
        return [
            {
                tag: 'blockquote'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'blockquote',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setBlockquote: ()=>({ commands })=>{
                    return commands.wrapIn(this.name);
                },
            toggleBlockquote: ()=>({ commands })=>{
                    return commands.toggleWrap(this.name);
                },
            unsetBlockquote: ()=>({ commands })=>{
                    return commands.lift(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Shift-b': ()=>this.editor.commands.toggleBlockquote()
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrappingInputRule"])({
                find: inputRegex,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-bold/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Bold": (()=>Bold),
    "default": (()=>Bold),
    "starInputRegex": (()=>starInputRegex),
    "starPasteRegex": (()=>starPasteRegex),
    "underscoreInputRegex": (()=>underscoreInputRegex),
    "underscorePasteRegex": (()=>underscorePasteRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Matches bold text via `**` as input.
 */ const starInputRegex = /(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/;
/**
 * Matches bold text via `**` while pasting.
 */ const starPasteRegex = /(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g;
/**
 * Matches bold text via `__` as input.
 */ const underscoreInputRegex = /(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/;
/**
 * Matches bold text via `__` while pasting.
 */ const underscorePasteRegex = /(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g;
/**
 * This extension allows you to mark text as bold.
 * @see https://tiptap.dev/api/marks/bold
 */ const Bold = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mark"].create({
    name: 'bold',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    parseHTML () {
        return [
            {
                tag: 'strong'
            },
            {
                tag: 'b',
                getAttrs: (node)=>node.style.fontWeight !== 'normal' && null
            },
            {
                style: 'font-weight=400',
                clearMark: (mark)=>mark.type.name === this.name
            },
            {
                style: 'font-weight',
                getAttrs: (value)=>/^(bold(er)?|[5-9]\d{2,})$/.test(value) && null
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'strong',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setBold: ()=>({ commands })=>{
                    return commands.setMark(this.name);
                },
            toggleBold: ()=>({ commands })=>{
                    return commands.toggleMark(this.name);
                },
            unsetBold: ()=>({ commands })=>{
                    return commands.unsetMark(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-b': ()=>this.editor.commands.toggleBold(),
            'Mod-B': ()=>this.editor.commands.toggleBold()
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: starInputRegex,
                type: this.type
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: underscoreInputRegex,
                type: this.type
            })
        ];
    },
    addPasteRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: starPasteRegex,
                type: this.type
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: underscorePasteRegex,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-bullet-list/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BulletList": (()=>BulletList),
    "default": (()=>BulletList),
    "inputRegex": (()=>inputRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
const ListItemName = 'listItem';
const TextStyleName = 'textStyle';
/**
 * Matches a bullet list to a dash or asterisk.
 */ const inputRegex = /^\s*([-+*])\s$/;
/**
 * This extension allows you to create bullet lists.
 * This requires the ListItem extension
 * @see https://tiptap.dev/api/nodes/bullet-list
 * @see https://tiptap.dev/api/nodes/list-item.
 */ const BulletList = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'bulletList',
    addOptions () {
        return {
            itemTypeName: 'listItem',
            HTMLAttributes: {},
            keepMarks: false,
            keepAttributes: false
        };
    },
    group: 'block list',
    content () {
        return `${this.options.itemTypeName}+`;
    },
    parseHTML () {
        return [
            {
                tag: 'ul'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'ul',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            toggleBulletList: ()=>({ commands, chain })=>{
                    if (this.options.keepAttributes) {
                        return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();
                    }
                    return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Shift-8': ()=>this.editor.commands.toggleBulletList()
        };
    },
    addInputRules () {
        let inputRule = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrappingInputRule"])({
            find: inputRegex,
            type: this.type
        });
        if (this.options.keepMarks || this.options.keepAttributes) {
            inputRule = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrappingInputRule"])({
                find: inputRegex,
                type: this.type,
                keepMarks: this.options.keepMarks,
                keepAttributes: this.options.keepAttributes,
                getAttributes: ()=>{
                    return this.editor.getAttributes(TextStyleName);
                },
                editor: this.editor
            });
        }
        return [
            inputRule
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-code/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Code": (()=>Code),
    "default": (()=>Code),
    "inputRegex": (()=>inputRegex),
    "pasteRegex": (()=>pasteRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Regular expressions to match inline code blocks enclosed in backticks.
 *  It matches:
 *     - An opening backtick, followed by
 *     - Any text that doesn't include a backtick (captured for marking), followed by
 *     - A closing backtick.
 *  This ensures that any text between backticks is formatted as code,
 *  regardless of the surrounding characters (exception being another backtick).
 */ const inputRegex = /(^|[^`])`([^`]+)`(?!`)/;
/**
 * Matches inline code while pasting.
 */ const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g;
/**
 * This extension allows you to mark text as inline code.
 * @see https://tiptap.dev/api/marks/code
 */ const Code = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mark"].create({
    name: 'code',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    excludes: '_',
    code: true,
    exitable: true,
    parseHTML () {
        return [
            {
                tag: 'code'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'code',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setCode: ()=>({ commands })=>{
                    return commands.setMark(this.name);
                },
            toggleCode: ()=>({ commands })=>{
                    return commands.toggleMark(this.name);
                },
            unsetCode: ()=>({ commands })=>{
                    return commands.unsetMark(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-e': ()=>this.editor.commands.toggleCode()
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: inputRegex,
                type: this.type
            })
        ];
    },
    addPasteRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: pasteRegex,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-code-block/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CodeBlock": (()=>CodeBlock),
    "backtickInputRegex": (()=>backtickInputRegex),
    "default": (()=>CodeBlock),
    "tildeInputRegex": (()=>tildeInputRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
;
;
/**
 * Matches a code block with backticks.
 */ const backtickInputRegex = /^```([a-z]+)?[\s\n]$/;
/**
 * Matches a code block with tildes.
 */ const tildeInputRegex = /^~~~([a-z]+)?[\s\n]$/;
/**
 * This extension allows you to create code blocks.
 * @see https://tiptap.dev/api/nodes/code-block
 */ const CodeBlock = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'codeBlock',
    addOptions () {
        return {
            languageClassPrefix: 'language-',
            exitOnTripleEnter: true,
            exitOnArrowDown: true,
            defaultLanguage: null,
            HTMLAttributes: {}
        };
    },
    content: 'text*',
    marks: '',
    group: 'block',
    code: true,
    defining: true,
    addAttributes () {
        return {
            language: {
                default: this.options.defaultLanguage,
                parseHTML: (element)=>{
                    var _a;
                    const { languageClassPrefix } = this.options;
                    const classNames = [
                        ...((_a = element.firstElementChild) === null || _a === void 0 ? void 0 : _a.classList) || []
                    ];
                    const languages = classNames.filter((className)=>className.startsWith(languageClassPrefix)).map((className)=>className.replace(languageClassPrefix, ''));
                    const language = languages[0];
                    if (!language) {
                        return null;
                    }
                    return language;
                },
                rendered: false
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: 'pre',
                preserveWhitespace: 'full'
            }
        ];
    },
    renderHTML ({ node, HTMLAttributes }) {
        return [
            'pre',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            [
                'code',
                {
                    class: node.attrs.language ? this.options.languageClassPrefix + node.attrs.language : null
                },
                0
            ]
        ];
    },
    addCommands () {
        return {
            setCodeBlock: (attributes)=>({ commands })=>{
                    return commands.setNode(this.name, attributes);
                },
            toggleCodeBlock: (attributes)=>({ commands })=>{
                    return commands.toggleNode(this.name, 'paragraph', attributes);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Alt-c': ()=>this.editor.commands.toggleCodeBlock(),
            // remove code block when at start of document or code block is empty
            Backspace: ()=>{
                const { empty, $anchor } = this.editor.state.selection;
                const isAtStart = $anchor.pos === 1;
                if (!empty || $anchor.parent.type.name !== this.name) {
                    return false;
                }
                if (isAtStart || !$anchor.parent.textContent.length) {
                    return this.editor.commands.clearNodes();
                }
                return false;
            },
            // exit node on triple enter
            Enter: ({ editor })=>{
                if (!this.options.exitOnTripleEnter) {
                    return false;
                }
                const { state } = editor;
                const { selection } = state;
                const { $from, empty } = selection;
                if (!empty || $from.parent.type !== this.type) {
                    return false;
                }
                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;
                const endsWithDoubleNewline = $from.parent.textContent.endsWith('\n\n');
                if (!isAtEnd || !endsWithDoubleNewline) {
                    return false;
                }
                return editor.chain().command(({ tr })=>{
                    tr.delete($from.pos - 2, $from.pos);
                    return true;
                }).exitCode().run();
            },
            // exit node on arrow down
            ArrowDown: ({ editor })=>{
                if (!this.options.exitOnArrowDown) {
                    return false;
                }
                const { state } = editor;
                const { selection, doc } = state;
                const { $from, empty } = selection;
                if (!empty || $from.parent.type !== this.type) {
                    return false;
                }
                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;
                if (!isAtEnd) {
                    return false;
                }
                const after = $from.after();
                if (after === undefined) {
                    return false;
                }
                const nodeAfter = doc.nodeAt(after);
                if (nodeAfter) {
                    return editor.commands.command(({ tr })=>{
                        tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Selection"].near(doc.resolve(after)));
                        return true;
                    });
                }
                return editor.commands.exitCode();
            }
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["textblockTypeInputRule"])({
                find: backtickInputRegex,
                type: this.type,
                getAttributes: (match)=>({
                        language: match[1]
                    })
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["textblockTypeInputRule"])({
                find: tildeInputRegex,
                type: this.type,
                getAttributes: (match)=>({
                        language: match[1]
                    })
            })
        ];
    },
    addProseMirrorPlugins () {
        return [
            // this plugin creates a code block for pasted content from VS Code
            // we can also detect the copied code language
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
                key: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"]('codeBlockVSCodeHandler'),
                props: {
                    handlePaste: (view, event)=>{
                        if (!event.clipboardData) {
                            return false;
                        }
                        // don’t create a new code block within code blocks
                        if (this.editor.isActive(this.type.name)) {
                            return false;
                        }
                        const text = event.clipboardData.getData('text/plain');
                        const vscode = event.clipboardData.getData('vscode-editor-data');
                        const vscodeData = vscode ? JSON.parse(vscode) : undefined;
                        const language = vscodeData === null || vscodeData === void 0 ? void 0 : vscodeData.mode;
                        if (!text || !language) {
                            return false;
                        }
                        const { tr, schema } = view.state;
                        // prepare a text node
                        // strip carriage return chars from text pasted as code
                        // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd
                        const textNode = schema.text(text.replace(/\r\n?/g, '\n'));
                        // create a code block with the text node
                        // replace selection with the code block
                        tr.replaceSelectionWith(this.type.create({
                            language
                        }, textNode));
                        if (tr.selection.$from.parent.type !== this.type) {
                            // put cursor inside the newly created code block
                            tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextSelection"].near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))));
                        }
                        // store meta information
                        // this is useful for other plugins that depends on the paste event
                        // like the paste rule plugin
                        tr.setMeta('paste', true);
                        view.dispatch(tr);
                        return true;
                    }
                }
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-document/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Document": (()=>Document),
    "default": (()=>Document)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * The default document node which represents the top level node of the editor.
 * @see https://tiptap.dev/api/nodes/document
 */ const Document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'doc',
    topNode: true,
    content: 'block+'
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-dropcursor/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dropcursor": (()=>Dropcursor),
    "default": (()=>Dropcursor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/dropcursor/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-dropcursor/dist/index.js [app-ssr] (ecmascript)");
;
;
/**
 * This extension allows you to add a drop cursor to your editor.
 * A drop cursor is a line that appears when you drag and drop content
 * inbetween nodes.
 * @see https://tiptap.dev/api/extensions/dropcursor
 */ const Dropcursor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'dropCursor',
    addOptions () {
        return {
            color: 'currentColor',
            width: 1,
            class: undefined
        };
    },
    addProseMirrorPlugins () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dropCursor"])(this.options)
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-gapcursor/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Gapcursor": (()=>Gapcursor),
    "default": (()=>Gapcursor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/gapcursor/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-gapcursor/dist/index.js [app-ssr] (ecmascript)");
;
;
/**
 * This extension allows you to add a gap cursor to your editor.
 * A gap cursor is a cursor that appears when you click on a place
 * where no content is present, for example inbetween nodes.
 * @see https://tiptap.dev/api/extensions/gapcursor
 */ const Gapcursor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'gapCursor',
    addProseMirrorPlugins () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["gapCursor"])()
        ];
    },
    extendNodeSchema (extension) {
        var _a;
        const context = {
            name: extension.name,
            options: extension.options,
            storage: extension.storage
        };
        return {
            allowGapCursor: (_a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callOrReturn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getExtensionField"])(extension, 'allowGapCursor', context))) !== null && _a !== void 0 ? _a : null
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-hard-break/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HardBreak": (()=>HardBreak),
    "default": (()=>HardBreak)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to insert hard breaks.
 * @see https://www.tiptap.dev/api/nodes/hard-break
 */ const HardBreak = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'hardBreak',
    addOptions () {
        return {
            keepMarks: true,
            HTMLAttributes: {}
        };
    },
    inline: true,
    group: 'inline',
    selectable: false,
    linebreakReplacement: true,
    parseHTML () {
        return [
            {
                tag: 'br'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'br',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes)
        ];
    },
    renderText () {
        return '\n';
    },
    addCommands () {
        return {
            setHardBreak: ()=>({ commands, chain, state, editor })=>{
                    return commands.first([
                        ()=>commands.exitCode(),
                        ()=>commands.command(()=>{
                                const { selection, storedMarks } = state;
                                if (selection.$from.parent.type.spec.isolating) {
                                    return false;
                                }
                                const { keepMarks } = this.options;
                                const { splittableMarks } = editor.extensionManager;
                                const marks = storedMarks || selection.$to.parentOffset && selection.$from.marks();
                                return chain().insertContent({
                                    type: this.name
                                }).command(({ tr, dispatch })=>{
                                    if (dispatch && marks && keepMarks) {
                                        const filteredMarks = marks.filter((mark)=>splittableMarks.includes(mark.type.name));
                                        tr.ensureMarks(filteredMarks);
                                    }
                                    return true;
                                }).run();
                            })
                    ]);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Enter': ()=>this.editor.commands.setHardBreak(),
            'Shift-Enter': ()=>this.editor.commands.setHardBreak()
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-heading/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heading": (()=>Heading),
    "default": (()=>Heading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create headings.
 * @see https://www.tiptap.dev/api/nodes/heading
 */ const Heading = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'heading',
    addOptions () {
        return {
            levels: [
                1,
                2,
                3,
                4,
                5,
                6
            ],
            HTMLAttributes: {}
        };
    },
    content: 'inline*',
    group: 'block',
    defining: true,
    addAttributes () {
        return {
            level: {
                default: 1,
                rendered: false
            }
        };
    },
    parseHTML () {
        return this.options.levels.map((level)=>({
                tag: `h${level}`,
                attrs: {
                    level
                }
            }));
    },
    renderHTML ({ node, HTMLAttributes }) {
        const hasLevel = this.options.levels.includes(node.attrs.level);
        const level = hasLevel ? node.attrs.level : this.options.levels[0];
        return [
            `h${level}`,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setHeading: (attributes)=>({ commands })=>{
                    if (!this.options.levels.includes(attributes.level)) {
                        return false;
                    }
                    return commands.setNode(this.name, attributes);
                },
            toggleHeading: (attributes)=>({ commands })=>{
                    if (!this.options.levels.includes(attributes.level)) {
                        return false;
                    }
                    return commands.toggleNode(this.name, 'paragraph', attributes);
                }
        };
    },
    addKeyboardShortcuts () {
        return this.options.levels.reduce((items, level)=>({
                ...items,
                ...{
                    [`Mod-Alt-${level}`]: ()=>this.editor.commands.toggleHeading({
                            level
                        })
                }
            }), {});
    },
    addInputRules () {
        return this.options.levels.map((level)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["textblockTypeInputRule"])({
                find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\s$`),
                type: this.type,
                getAttributes: {
                    level
                }
            });
        });
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-history/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "History": (()=>History),
    "default": (()=>History)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/history/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-history/dist/index.js [app-ssr] (ecmascript)");
;
;
/**
 * This extension allows you to undo and redo recent changes.
 * @see https://www.tiptap.dev/api/extensions/history
 *
 * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove
 * the `history` extension, as it is not compatible with the `collaboration` extension.
 *
 * `@tiptap/extension-collaboration` uses its own history implementation.
 */ const History = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'history',
    addOptions () {
        return {
            depth: 100,
            newGroupDelay: 500
        };
    },
    addCommands () {
        return {
            undo: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undo"])(state, dispatch);
                },
            redo: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["redo"])(state, dispatch);
                }
        };
    },
    addProseMirrorPlugins () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["history"])(this.options)
        ];
    },
    addKeyboardShortcuts () {
        return {
            'Mod-z': ()=>this.editor.commands.undo(),
            'Shift-Mod-z': ()=>this.editor.commands.redo(),
            'Mod-y': ()=>this.editor.commands.redo(),
            // Russian keyboard layouts
            'Mod-я': ()=>this.editor.commands.undo(),
            'Shift-Mod-я': ()=>this.editor.commands.redo()
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-horizontal-rule/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HorizontalRule": (()=>HorizontalRule),
    "default": (()=>HorizontalRule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
;
;
/**
 * This extension allows you to insert horizontal rules.
 * @see https://www.tiptap.dev/api/nodes/horizontal-rule
 */ const HorizontalRule = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'horizontalRule',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    group: 'block',
    parseHTML () {
        return [
            {
                tag: 'hr'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'hr',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes)
        ];
    },
    addCommands () {
        return {
            setHorizontalRule: ()=>({ chain, state })=>{
                    // Check if we can insert the node at the current selection
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canInsertNode"])(state, state.schema.nodes[this.name])) {
                        return false;
                    }
                    const { selection } = state;
                    const { $from: $originFrom, $to: $originTo } = selection;
                    const currentChain = chain();
                    if ($originFrom.parentOffset === 0) {
                        currentChain.insertContentAt({
                            from: Math.max($originFrom.pos - 1, 0),
                            to: $originTo.pos
                        }, {
                            type: this.name
                        });
                    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNodeSelection"])(selection)) {
                        currentChain.insertContentAt($originTo.pos, {
                            type: this.name
                        });
                    } else {
                        currentChain.insertContent({
                            type: this.name
                        });
                    }
                    return currentChain// set cursor after horizontal rule
                    .command(({ tr, dispatch })=>{
                        var _a;
                        if (dispatch) {
                            const { $to } = tr.selection;
                            const posAfter = $to.end();
                            if ($to.nodeAfter) {
                                if ($to.nodeAfter.isTextblock) {
                                    tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextSelection"].create(tr.doc, $to.pos + 1));
                                } else if ($to.nodeAfter.isBlock) {
                                    tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NodeSelection"].create(tr.doc, $to.pos));
                                } else {
                                    tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextSelection"].create(tr.doc, $to.pos));
                                }
                            } else {
                                // add node after horizontal rule if it’s the end of the document
                                const node = (_a = $to.parent.type.contentMatch.defaultType) === null || _a === void 0 ? void 0 : _a.create();
                                if (node) {
                                    tr.insert(posAfter, node);
                                    tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextSelection"].create(tr.doc, posAfter + 1));
                                }
                            }
                            tr.scrollIntoView();
                        }
                        return true;
                    }).run();
                }
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["nodeInputRule"])({
                find: /^(?:---|—-|___\s|\*\*\*\s)$/,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-italic/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Italic": (()=>Italic),
    "default": (()=>Italic),
    "starInputRegex": (()=>starInputRegex),
    "starPasteRegex": (()=>starPasteRegex),
    "underscoreInputRegex": (()=>underscoreInputRegex),
    "underscorePasteRegex": (()=>underscorePasteRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Matches an italic to a *italic* on input.
 */ const starInputRegex = /(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/;
/**
 * Matches an italic to a *italic* on paste.
 */ const starPasteRegex = /(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g;
/**
 * Matches an italic to a _italic_ on input.
 */ const underscoreInputRegex = /(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/;
/**
 * Matches an italic to a _italic_ on paste.
 */ const underscorePasteRegex = /(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g;
/**
 * This extension allows you to create italic text.
 * @see https://www.tiptap.dev/api/marks/italic
 */ const Italic = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mark"].create({
    name: 'italic',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    parseHTML () {
        return [
            {
                tag: 'em'
            },
            {
                tag: 'i',
                getAttrs: (node)=>node.style.fontStyle !== 'normal' && null
            },
            {
                style: 'font-style=normal',
                clearMark: (mark)=>mark.type.name === this.name
            },
            {
                style: 'font-style=italic'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'em',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setItalic: ()=>({ commands })=>{
                    return commands.setMark(this.name);
                },
            toggleItalic: ()=>({ commands })=>{
                    return commands.toggleMark(this.name);
                },
            unsetItalic: ()=>({ commands })=>{
                    return commands.unsetMark(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-i': ()=>this.editor.commands.toggleItalic(),
            'Mod-I': ()=>this.editor.commands.toggleItalic()
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: starInputRegex,
                type: this.type
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: underscoreInputRegex,
                type: this.type
            })
        ];
    },
    addPasteRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: starPasteRegex,
                type: this.type
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: underscorePasteRegex,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-list-item/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ListItem": (()=>ListItem),
    "default": (()=>ListItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create list items.
 * @see https://www.tiptap.dev/api/nodes/list-item
 */ const ListItem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'listItem',
    addOptions () {
        return {
            HTMLAttributes: {},
            bulletListTypeName: 'bulletList',
            orderedListTypeName: 'orderedList'
        };
    },
    content: 'paragraph block*',
    defining: true,
    parseHTML () {
        return [
            {
                tag: 'li'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'li',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addKeyboardShortcuts () {
        return {
            Enter: ()=>this.editor.commands.splitListItem(this.name),
            Tab: ()=>this.editor.commands.sinkListItem(this.name),
            'Shift-Tab': ()=>this.editor.commands.liftListItem(this.name)
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-ordered-list/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OrderedList": (()=>OrderedList),
    "default": (()=>OrderedList),
    "inputRegex": (()=>inputRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
const ListItemName = 'listItem';
const TextStyleName = 'textStyle';
/**
 * Matches an ordered list to a 1. on input (or any number followed by a dot).
 */ const inputRegex = /^(\d+)\.\s$/;
/**
 * This extension allows you to create ordered lists.
 * This requires the ListItem extension
 * @see https://www.tiptap.dev/api/nodes/ordered-list
 * @see https://www.tiptap.dev/api/nodes/list-item
 */ const OrderedList = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'orderedList',
    addOptions () {
        return {
            itemTypeName: 'listItem',
            HTMLAttributes: {},
            keepMarks: false,
            keepAttributes: false
        };
    },
    group: 'block list',
    content () {
        return `${this.options.itemTypeName}+`;
    },
    addAttributes () {
        return {
            start: {
                default: 1,
                parseHTML: (element)=>{
                    return element.hasAttribute('start') ? parseInt(element.getAttribute('start') || '', 10) : 1;
                }
            },
            type: {
                default: null,
                parseHTML: (element)=>element.getAttribute('type')
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: 'ol'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        const { start, ...attributesWithoutStart } = HTMLAttributes;
        return start === 1 ? [
            'ol',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, attributesWithoutStart),
            0
        ] : [
            'ol',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            toggleOrderedList: ()=>({ commands, chain })=>{
                    if (this.options.keepAttributes) {
                        return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();
                    }
                    return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Shift-7': ()=>this.editor.commands.toggleOrderedList()
        };
    },
    addInputRules () {
        let inputRule = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrappingInputRule"])({
            find: inputRegex,
            type: this.type,
            getAttributes: (match)=>({
                    start: +match[1]
                }),
            joinPredicate: (match, node)=>node.childCount + node.attrs.start === +match[1]
        });
        if (this.options.keepMarks || this.options.keepAttributes) {
            inputRule = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrappingInputRule"])({
                find: inputRegex,
                type: this.type,
                keepMarks: this.options.keepMarks,
                keepAttributes: this.options.keepAttributes,
                getAttributes: (match)=>({
                        start: +match[1],
                        ...this.editor.getAttributes(TextStyleName)
                    }),
                joinPredicate: (match, node)=>node.childCount + node.attrs.start === +match[1],
                editor: this.editor
            });
        }
        return [
            inputRule
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-paragraph/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Paragraph": (()=>Paragraph),
    "default": (()=>Paragraph)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create paragraphs.
 * @see https://www.tiptap.dev/api/nodes/paragraph
 */ const Paragraph = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'paragraph',
    priority: 1000,
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    group: 'block',
    content: 'inline*',
    parseHTML () {
        return [
            {
                tag: 'p'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'p',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setParagraph: ()=>({ commands })=>{
                    return commands.setNode(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Alt-0': ()=>this.editor.commands.setParagraph()
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-strike/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Strike": (()=>Strike),
    "default": (()=>Strike),
    "inputRegex": (()=>inputRegex),
    "pasteRegex": (()=>pasteRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Matches a strike to a ~~strike~~ on input.
 */ const inputRegex = /(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/;
/**
 * Matches a strike to a ~~strike~~ on paste.
 */ const pasteRegex = /(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g;
/**
 * This extension allows you to create strike text.
 * @see https://www.tiptap.dev/api/marks/strike
 */ const Strike = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mark"].create({
    name: 'strike',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    parseHTML () {
        return [
            {
                tag: 's'
            },
            {
                tag: 'del'
            },
            {
                tag: 'strike'
            },
            {
                style: 'text-decoration',
                consuming: false,
                getAttrs: (style)=>style.includes('line-through') ? {} : false
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            's',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setStrike: ()=>({ commands })=>{
                    return commands.setMark(this.name);
                },
            toggleStrike: ()=>({ commands })=>{
                    return commands.toggleMark(this.name);
                },
            unsetStrike: ()=>({ commands })=>{
                    return commands.unsetMark(this.name);
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            'Mod-Shift-s': ()=>this.editor.commands.toggleStrike()
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markInputRule"])({
                find: inputRegex,
                type: this.type
            })
        ];
    },
    addPasteRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: pasteRegex,
                type: this.type
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-text/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>Text),
    "default": (()=>Text)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create text nodes.
 * @see https://www.tiptap.dev/api/nodes/text
 */ const Text = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'text',
    group: 'inline'
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/starter-kit/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "StarterKit": (()=>StarterKit),
    "default": (()=>StarterKit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$blockquote$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-blockquote/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bold$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-bold/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bullet$2d$list$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-bullet-list/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-code/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2d$block$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-code-block/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$document$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-document/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-dropcursor/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-gapcursor/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$hard$2d$break$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-hard-break/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$heading$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-heading/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-history/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$horizontal$2d$rule$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-horizontal-rule/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$italic$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-italic/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$list$2d$item$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-list-item/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$ordered$2d$list$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-ordered-list/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$paragraph$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-paragraph/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$strike$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-strike/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$text$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-text/dist/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * The starter kit is a collection of essential editor extensions.
 *
 * It’s a good starting point for building your own editor.
 */ const StarterKit = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Extension"].create({
    name: 'starterKit',
    addExtensions () {
        const extensions = [];
        if (this.options.bold !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bold$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Bold"].configure(this.options.bold));
        }
        if (this.options.blockquote !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$blockquote$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Blockquote"].configure(this.options.blockquote));
        }
        if (this.options.bulletList !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bullet$2d$list$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BulletList"].configure(this.options.bulletList));
        }
        if (this.options.code !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Code"].configure(this.options.code));
        }
        if (this.options.codeBlock !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2d$block$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CodeBlock"].configure(this.options.codeBlock));
        }
        if (this.options.document !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$document$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Document"].configure(this.options.document));
        }
        if (this.options.dropcursor !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Dropcursor"].configure(this.options.dropcursor));
        }
        if (this.options.gapcursor !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Gapcursor"].configure(this.options.gapcursor));
        }
        if (this.options.hardBreak !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$hard$2d$break$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HardBreak"].configure(this.options.hardBreak));
        }
        if (this.options.heading !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$heading$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Heading"].configure(this.options.heading));
        }
        if (this.options.history !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["History"].configure(this.options.history));
        }
        if (this.options.horizontalRule !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$horizontal$2d$rule$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HorizontalRule"].configure(this.options.horizontalRule));
        }
        if (this.options.italic !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$italic$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Italic"].configure(this.options.italic));
        }
        if (this.options.listItem !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$list$2d$item$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ListItem"].configure(this.options.listItem));
        }
        if (this.options.orderedList !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$ordered$2d$list$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OrderedList"].configure(this.options.orderedList));
        }
        if (this.options.paragraph !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$paragraph$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Paragraph"].configure(this.options.paragraph));
        }
        if (this.options.strike !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$strike$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Strike"].configure(this.options.strike));
        }
        if (this.options.text !== false) {
            extensions.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$text$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Text"].configure(this.options.text));
        }
        return extensions;
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-link/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Link": (()=>Link),
    "default": (()=>Link),
    "isAllowedUri": (()=>isAllowedUri),
    "pasteRegex": (()=>pasteRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkifyjs/dist/linkify.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
;
;
;
/**
 * Check if the provided tokens form a valid link structure, which can either be a single link token
 * or a link token surrounded by parentheses or square brackets.
 *
 * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid
 * top-level domain (TLD) is immediately followed by an invalid character, like a number. For
 * example, with the `find` method from Linkify, entering `example.com1` would result in
 * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`
 * method, we can perform more comprehensive validation on the input text.
 */ function isValidLinkStructure(tokens) {
    if (tokens.length === 1) {
        return tokens[0].isLink;
    }
    if (tokens.length === 3 && tokens[1].isLink) {
        return [
            '()',
            '[]'
        ].includes(tokens[0].value + tokens[2].value);
    }
    return false;
}
/**
 * This plugin allows you to automatically add links to your editor.
 * @param options The plugin options
 * @returns The plugin instance
 */ function autolink(options) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
        key: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"]('autolink'),
        appendTransaction: (transactions, oldState, newState)=>{
            /**
             * Does the transaction change the document?
             */ const docChanges = transactions.some((transaction)=>transaction.docChanged) && !oldState.doc.eq(newState.doc);
            /**
             * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.
             */ const preventAutolink = transactions.some((transaction)=>transaction.getMeta('preventAutolink'));
            /**
             * Prevent autolink if the transaction is not a document change
             * or if the transaction has the meta `preventAutolink`.
             */ if (!docChanges || preventAutolink) {
                return;
            }
            const { tr } = newState;
            const transform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["combineTransactionSteps"])(oldState.doc, [
                ...transactions
            ]);
            const changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChangedRanges"])(transform);
            changes.forEach(({ newRange })=>{
                // Now let’s see if we can add new links.
                const nodesInChangedRanges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findChildrenInRange"])(newState.doc, newRange, (node)=>node.isTextblock);
                let textBlock;
                let textBeforeWhitespace;
                if (nodesInChangedRanges.length > 1) {
                    // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).
                    textBlock = nodesInChangedRanges[0];
                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, textBlock.pos + textBlock.node.nodeSize, undefined, ' ');
                } else if (nodesInChangedRanges.length && newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ').endsWith(' ')) {
                    textBlock = nodesInChangedRanges[0];
                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, newRange.to, undefined, ' ');
                }
                if (textBlock && textBeforeWhitespace) {
                    const wordsBeforeWhitespace = textBeforeWhitespace.split(' ').filter((s)=>s !== '');
                    if (wordsBeforeWhitespace.length <= 0) {
                        return false;
                    }
                    const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1];
                    const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace);
                    if (!lastWordBeforeSpace) {
                        return false;
                    }
                    const linksBeforeSpace = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tokenize"])(lastWordBeforeSpace).map((t)=>t.toObject(options.defaultProtocol));
                    if (!isValidLinkStructure(linksBeforeSpace)) {
                        return false;
                    }
                    linksBeforeSpace.filter((link)=>link.isLink)// Calculate link position.
                    .map((link)=>({
                            ...link,
                            from: lastWordAndBlockOffset + link.start + 1,
                            to: lastWordAndBlockOffset + link.end + 1
                        }))// ignore link inside code mark
                    .filter((link)=>{
                        if (!newState.schema.marks.code) {
                            return true;
                        }
                        return !newState.doc.rangeHasMark(link.from, link.to, newState.schema.marks.code);
                    })// validate link
                    .filter((link)=>options.validate(link.value))// check whether should autolink
                    .filter((link)=>options.shouldAutoLink(link.value))// Add link mark.
                    .forEach((link)=>{
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMarksBetween"])(link.from, link.to, newState.doc).some((item)=>item.mark.type === options.type)) {
                            return;
                        }
                        tr.addMark(link.from, link.to, options.type.create({
                            href: link.href
                        }));
                    });
                }
            });
            if (!tr.steps.length) {
                return;
            }
            return tr;
        }
    });
}
function clickHandler(options) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
        key: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"]('handleClickLink'),
        props: {
            handleClick: (view, pos, event)=>{
                var _a, _b;
                if (event.button !== 0) {
                    return false;
                }
                if (!view.editable) {
                    return false;
                }
                let a = event.target;
                const els = [];
                while(a.nodeName !== 'DIV'){
                    els.push(a);
                    a = a.parentNode;
                }
                if (!els.find((value)=>value.nodeName === 'A')) {
                    return false;
                }
                const attrs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAttributes"])(view.state, options.type.name);
                const link = event.target;
                const href = (_a = link === null || link === void 0 ? void 0 : link.href) !== null && _a !== void 0 ? _a : attrs.href;
                const target = (_b = link === null || link === void 0 ? void 0 : link.target) !== null && _b !== void 0 ? _b : attrs.target;
                if (link && href) {
                    window.open(href, target);
                    return true;
                }
                return false;
            }
        }
    });
}
function pasteHandler(options) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plugin"]({
        key: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PluginKey"]('handlePasteLink'),
        props: {
            handlePaste: (view, event, slice)=>{
                const { state } = view;
                const { selection } = state;
                const { empty } = selection;
                if (empty) {
                    return false;
                }
                let textContent = '';
                slice.content.forEach((node)=>{
                    textContent += node.textContent;
                });
                const link = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["find"])(textContent, {
                    defaultProtocol: options.defaultProtocol
                }).find((item)=>item.isLink && item.value === textContent);
                if (!textContent || !link) {
                    return false;
                }
                return options.editor.commands.setMark(options.type, {
                    href: link.href
                });
            }
        }
    });
}
const pasteRegex = /https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z]{2,}\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi;
// From DOMPurify
// https://github.com/cure53/DOMPurify/blob/main/src/regexp.js
// eslint-disable-next-line no-control-regex
const ATTR_WHITESPACE = /[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;
function isAllowedUri(uri, protocols) {
    const allowedProtocols = [
        'http',
        'https',
        'ftp',
        'ftps',
        'mailto',
        'tel',
        'callto',
        'sms',
        'cid',
        'xmpp'
    ];
    if (protocols) {
        protocols.forEach((protocol)=>{
            const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme;
            if (nextProtocol) {
                allowedProtocols.push(nextProtocol);
            }
        });
    }
    return !uri || uri.replace(ATTR_WHITESPACE, '').match(new RegExp(// eslint-disable-next-line no-useless-escape
    `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\-]+(?:[^a-z+.\-:]|$))`, 'i'));
}
/**
 * This extension allows you to create links.
 * @see https://www.tiptap.dev/api/marks/link
 */ const Link = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mark"].create({
    name: 'link',
    priority: 1000,
    keepOnSplit: false,
    exitable: true,
    onCreate () {
        if (this.options.validate && !this.options.shouldAutoLink) {
            // Copy the validate function to the shouldAutoLink option
            this.options.shouldAutoLink = this.options.validate;
            console.warn('The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.');
        }
        this.options.protocols.forEach((protocol)=>{
            if (typeof protocol === 'string') {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["registerCustomProtocol"])(protocol);
                return;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["registerCustomProtocol"])(protocol.scheme, protocol.optionalSlashes);
        });
    },
    onDestroy () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reset"])();
    },
    inclusive () {
        return this.options.autolink;
    },
    addOptions () {
        return {
            openOnClick: true,
            linkOnPaste: true,
            autolink: true,
            protocols: [],
            defaultProtocol: 'http',
            HTMLAttributes: {
                target: '_blank',
                rel: 'noopener noreferrer nofollow',
                class: null
            },
            isAllowedUri: (url, ctx)=>!!isAllowedUri(url, ctx.protocols),
            validate: (url)=>!!url,
            shouldAutoLink: (url)=>!!url
        };
    },
    addAttributes () {
        return {
            href: {
                default: null,
                parseHTML (element) {
                    return element.getAttribute('href');
                }
            },
            target: {
                default: this.options.HTMLAttributes.target
            },
            rel: {
                default: this.options.HTMLAttributes.rel
            },
            class: {
                default: this.options.HTMLAttributes.class
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: 'a[href]',
                getAttrs: (dom)=>{
                    const href = dom.getAttribute('href');
                    // prevent XSS attacks
                    if (!href || !this.options.isAllowedUri(href, {
                        defaultValidate: (url)=>!!isAllowedUri(url, this.options.protocols),
                        protocols: this.options.protocols,
                        defaultProtocol: this.options.defaultProtocol
                    })) {
                        return false;
                    }
                    return null;
                }
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        // prevent XSS attacks
        if (!this.options.isAllowedUri(HTMLAttributes.href, {
            defaultValidate: (href)=>!!isAllowedUri(href, this.options.protocols),
            protocols: this.options.protocols,
            defaultProtocol: this.options.defaultProtocol
        })) {
            // strip out the href
            return [
                'a',
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, {
                    ...HTMLAttributes,
                    href: ''
                }),
                0
            ];
        }
        return [
            'a',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    },
    addCommands () {
        return {
            setLink: (attributes)=>({ chain })=>{
                    const { href } = attributes;
                    if (!this.options.isAllowedUri(href, {
                        defaultValidate: (url)=>!!isAllowedUri(url, this.options.protocols),
                        protocols: this.options.protocols,
                        defaultProtocol: this.options.defaultProtocol
                    })) {
                        return false;
                    }
                    return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run();
                },
            toggleLink: (attributes)=>({ chain })=>{
                    const { href } = attributes;
                    if (!this.options.isAllowedUri(href, {
                        defaultValidate: (url)=>!!isAllowedUri(url, this.options.protocols),
                        protocols: this.options.protocols,
                        defaultProtocol: this.options.defaultProtocol
                    })) {
                        return false;
                    }
                    return chain().toggleMark(this.name, attributes, {
                        extendEmptyMarkRange: true
                    }).setMeta('preventAutolink', true).run();
                },
            unsetLink: ()=>({ chain })=>{
                    return chain().unsetMark(this.name, {
                        extendEmptyMarkRange: true
                    }).setMeta('preventAutolink', true).run();
                }
        };
    },
    addPasteRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["markPasteRule"])({
                find: (text)=>{
                    const foundLinks = [];
                    if (text) {
                        const { protocols, defaultProtocol } = this.options;
                        const links = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkifyjs$2f$dist$2f$linkify$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["find"])(text).filter((item)=>item.isLink && this.options.isAllowedUri(item.value, {
                                defaultValidate: (href)=>!!isAllowedUri(href, protocols),
                                protocols,
                                defaultProtocol
                            }));
                        if (links.length) {
                            links.forEach((link)=>foundLinks.push({
                                    text: link.value,
                                    data: {
                                        href: link.href
                                    },
                                    index: link.start
                                }));
                        }
                    }
                    return foundLinks;
                },
                type: this.type,
                getAttributes: (match)=>{
                    var _a;
                    return {
                        href: (_a = match.data) === null || _a === void 0 ? void 0 : _a.href
                    };
                }
            })
        ];
    },
    addProseMirrorPlugins () {
        const plugins = [];
        const { protocols, defaultProtocol } = this.options;
        if (this.options.autolink) {
            plugins.push(autolink({
                type: this.type,
                defaultProtocol: this.options.defaultProtocol,
                validate: (url)=>this.options.isAllowedUri(url, {
                        defaultValidate: (href)=>!!isAllowedUri(href, protocols),
                        protocols,
                        defaultProtocol
                    }),
                shouldAutoLink: this.options.shouldAutoLink
            }));
        }
        if (this.options.openOnClick === true) {
            plugins.push(clickHandler({
                type: this.type
            }));
        }
        if (this.options.linkOnPaste) {
            plugins.push(pasteHandler({
                editor: this.editor,
                defaultProtocol: this.options.defaultProtocol,
                type: this.type
            }));
        }
        return plugins;
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-image/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Image": (()=>Image),
    "default": (()=>Image),
    "inputRegex": (()=>inputRegex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * Matches an image to a ![image](src "title") on input.
 */ const inputRegex = /(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/;
/**
 * This extension allows you to insert images.
 * @see https://www.tiptap.dev/api/nodes/image
 */ const Image = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'image',
    addOptions () {
        return {
            inline: false,
            allowBase64: false,
            HTMLAttributes: {}
        };
    },
    inline () {
        return this.options.inline;
    },
    group () {
        return this.options.inline ? 'inline' : 'block';
    },
    draggable: true,
    addAttributes () {
        return {
            src: {
                default: null
            },
            alt: {
                default: null
            },
            title: {
                default: null
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: this.options.allowBase64 ? 'img[src]' : 'img[src]:not([src^="data:"])'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'img',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes)
        ];
    },
    addCommands () {
        return {
            setImage: (options)=>({ commands })=>{
                    return commands.insertContent({
                        type: this.name,
                        attrs: options
                    });
                }
        };
    },
    addInputRules () {
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["nodeInputRule"])({
                find: inputRegex,
                type: this.type,
                getAttributes: (match)=>{
                    const [, , alt, src, title] = match;
                    return {
                        src,
                        alt,
                        title
                    };
                }
            })
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-table/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Table": (()=>Table),
    "TableView": (()=>TableView),
    "createColGroup": (()=>createColGroup),
    "createTable": (()=>createTable),
    "default": (()=>Table),
    "updateColumns": (()=>updateColumns)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/state/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$pm$2f$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/pm/tables/dist/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-tables/dist/index.js [app-ssr] (ecmascript)");
;
;
;
function getColStyleDeclaration(minWidth, width) {
    if (width) {
        // apply the stored width unless it is below the configured minimum cell width
        return [
            'width',
            `${Math.max(width, minWidth)}px`
        ];
    }
    // set the minimum with on the column if it has no stored width
    return [
        'min-width',
        `${minWidth}px`
    ];
}
function updateColumns(node, colgroup, table, cellMinWidth, overrideCol, overrideValue) {
    var _a;
    let totalWidth = 0;
    let fixedWidth = true;
    let nextDOM = colgroup.firstChild;
    const row = node.firstChild;
    if (row !== null) {
        for(let i = 0, col = 0; i < row.childCount; i += 1){
            const { colspan, colwidth } = row.child(i).attrs;
            for(let j = 0; j < colspan; j += 1, col += 1){
                const hasWidth = overrideCol === col ? overrideValue : colwidth && colwidth[j];
                const cssWidth = hasWidth ? `${hasWidth}px` : '';
                totalWidth += hasWidth || cellMinWidth;
                if (!hasWidth) {
                    fixedWidth = false;
                }
                if (!nextDOM) {
                    const colElement = document.createElement('col');
                    const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth);
                    colElement.style.setProperty(propertyKey, propertyValue);
                    colgroup.appendChild(colElement);
                } else {
                    if (nextDOM.style.width !== cssWidth) {
                        const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth);
                        nextDOM.style.setProperty(propertyKey, propertyValue);
                    }
                    nextDOM = nextDOM.nextSibling;
                }
            }
        }
    }
    while(nextDOM){
        const after = nextDOM.nextSibling;
        (_a = nextDOM.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(nextDOM);
        nextDOM = after;
    }
    if (fixedWidth) {
        table.style.width = `${totalWidth}px`;
        table.style.minWidth = '';
    } else {
        table.style.width = '';
        table.style.minWidth = `${totalWidth}px`;
    }
}
class TableView {
    constructor(node, cellMinWidth){
        this.node = node;
        this.cellMinWidth = cellMinWidth;
        this.dom = document.createElement('div');
        this.dom.className = 'tableWrapper';
        this.table = this.dom.appendChild(document.createElement('table'));
        this.colgroup = this.table.appendChild(document.createElement('colgroup'));
        updateColumns(node, this.colgroup, this.table, cellMinWidth);
        this.contentDOM = this.table.appendChild(document.createElement('tbody'));
    }
    update(node) {
        if (node.type !== this.node.type) {
            return false;
        }
        this.node = node;
        updateColumns(node, this.colgroup, this.table, this.cellMinWidth);
        return true;
    }
    ignoreMutation(mutation) {
        return mutation.type === 'attributes' && (mutation.target === this.table || this.colgroup.contains(mutation.target));
    }
}
function createColGroup(node, cellMinWidth, overrideCol, overrideValue) {
    let totalWidth = 0;
    let fixedWidth = true;
    const cols = [];
    const row = node.firstChild;
    if (!row) {
        return {};
    }
    for(let i = 0, col = 0; i < row.childCount; i += 1){
        const { colspan, colwidth } = row.child(i).attrs;
        for(let j = 0; j < colspan; j += 1, col += 1){
            const hasWidth = overrideCol === col ? overrideValue : colwidth && colwidth[j];
            totalWidth += hasWidth || cellMinWidth;
            if (!hasWidth) {
                fixedWidth = false;
            }
            const [property, value] = getColStyleDeclaration(cellMinWidth, hasWidth);
            cols.push([
                'col',
                {
                    style: `${property}: ${value}`
                }
            ]);
        }
    }
    const tableWidth = fixedWidth ? `${totalWidth}px` : '';
    const tableMinWidth = fixedWidth ? '' : `${totalWidth}px`;
    const colgroup = [
        'colgroup',
        {},
        ...cols
    ];
    return {
        colgroup,
        tableWidth,
        tableMinWidth
    };
}
function createCell(cellType, cellContent) {
    if (cellContent) {
        return cellType.createChecked(null, cellContent);
    }
    return cellType.createAndFill();
}
function getTableNodeTypes(schema) {
    if (schema.cached.tableNodeTypes) {
        return schema.cached.tableNodeTypes;
    }
    const roles = {};
    Object.keys(schema.nodes).forEach((type)=>{
        const nodeType = schema.nodes[type];
        if (nodeType.spec.tableRole) {
            roles[nodeType.spec.tableRole] = nodeType;
        }
    });
    schema.cached.tableNodeTypes = roles;
    return roles;
}
function createTable(schema, rowsCount, colsCount, withHeaderRow, cellContent) {
    const types = getTableNodeTypes(schema);
    const headerCells = [];
    const cells = [];
    for(let index = 0; index < colsCount; index += 1){
        const cell = createCell(types.cell, cellContent);
        if (cell) {
            cells.push(cell);
        }
        if (withHeaderRow) {
            const headerCell = createCell(types.header_cell, cellContent);
            if (headerCell) {
                headerCells.push(headerCell);
            }
        }
    }
    const rows = [];
    for(let index = 0; index < rowsCount; index += 1){
        rows.push(types.row.createChecked(null, withHeaderRow && index === 0 ? headerCells : cells));
    }
    return types.table.createChecked(null, rows);
}
function isCellSelection(value) {
    return value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CellSelection"];
}
const deleteTableWhenAllCellsSelected = ({ editor })=>{
    const { selection } = editor.state;
    if (!isCellSelection(selection)) {
        return false;
    }
    let cellCount = 0;
    const table = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findParentNodeClosestToPos"])(selection.ranges[0].$from, (node)=>{
        return node.type.name === 'table';
    });
    table === null || table === void 0 ? void 0 : table.node.descendants((node)=>{
        if (node.type.name === 'table') {
            return false;
        }
        if ([
            'tableCell',
            'tableHeader'
        ].includes(node.type.name)) {
            cellCount += 1;
        }
    });
    const allCellsSelected = cellCount === selection.ranges.length;
    if (!allCellsSelected) {
        return false;
    }
    editor.commands.deleteTable();
    return true;
};
/**
 * This extension allows you to create tables.
 * @see https://www.tiptap.dev/api/nodes/table
 */ const Table = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'table',
    // @ts-ignore
    addOptions () {
        return {
            HTMLAttributes: {},
            resizable: false,
            handleWidth: 5,
            cellMinWidth: 25,
            // TODO: fix
            View: TableView,
            lastColumnResizable: true,
            allowTableNodeSelection: false
        };
    },
    content: 'tableRow+',
    tableRole: 'table',
    isolating: true,
    group: 'block',
    parseHTML () {
        return [
            {
                tag: 'table'
            }
        ];
    },
    renderHTML ({ node, HTMLAttributes }) {
        const { colgroup, tableWidth, tableMinWidth } = createColGroup(node, this.options.cellMinWidth);
        const table = [
            'table',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes, {
                style: tableWidth ? `width: ${tableWidth}` : `min-width: ${tableMinWidth}`
            }),
            colgroup,
            [
                'tbody',
                0
            ]
        ];
        return table;
    },
    addCommands () {
        return {
            insertTable: ({ rows = 3, cols = 3, withHeaderRow = true } = {})=>({ tr, dispatch, editor })=>{
                    const node = createTable(editor.schema, rows, cols, withHeaderRow);
                    if (dispatch) {
                        const offset = tr.selection.from + 1;
                        tr.replaceSelectionWith(node).scrollIntoView().setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextSelection"].near(tr.doc.resolve(offset)));
                    }
                    return true;
                },
            addColumnBefore: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addColumnBefore"])(state, dispatch);
                },
            addColumnAfter: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addColumnAfter"])(state, dispatch);
                },
            deleteColumn: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteColumn"])(state, dispatch);
                },
            addRowBefore: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addRowBefore"])(state, dispatch);
                },
            addRowAfter: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addRowAfter"])(state, dispatch);
                },
            deleteRow: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteRow"])(state, dispatch);
                },
            deleteTable: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteTable"])(state, dispatch);
                },
            mergeCells: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeCells"])(state, dispatch);
                },
            splitCell: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["splitCell"])(state, dispatch);
                },
            toggleHeaderColumn: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHeader"])('column')(state, dispatch);
                },
            toggleHeaderRow: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHeader"])('row')(state, dispatch);
                },
            toggleHeaderCell: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHeaderCell"])(state, dispatch);
                },
            mergeOrSplit: ()=>({ state, dispatch })=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeCells"])(state, dispatch)) {
                        return true;
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["splitCell"])(state, dispatch);
                },
            setCellAttribute: (name, value)=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setCellAttr"])(name, value)(state, dispatch);
                },
            goToNextCell: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["goToNextCell"])(1)(state, dispatch);
                },
            goToPreviousCell: ()=>({ state, dispatch })=>{
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["goToNextCell"])(-1)(state, dispatch);
                },
            fixTables: ()=>({ state, dispatch })=>{
                    if (dispatch) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fixTables"])(state);
                    }
                    return true;
                },
            setCellSelection: (position)=>({ tr, dispatch })=>{
                    if (dispatch) {
                        const selection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CellSelection"].create(tr.doc, position.anchorCell, position.headCell);
                        // @ts-ignore
                        tr.setSelection(selection);
                    }
                    return true;
                }
        };
    },
    addKeyboardShortcuts () {
        return {
            Tab: ()=>{
                if (this.editor.commands.goToNextCell()) {
                    return true;
                }
                if (!this.editor.can().addRowAfter()) {
                    return false;
                }
                return this.editor.chain().addRowAfter().goToNextCell().run();
            },
            'Shift-Tab': ()=>this.editor.commands.goToPreviousCell(),
            Backspace: deleteTableWhenAllCellsSelected,
            'Mod-Backspace': deleteTableWhenAllCellsSelected,
            Delete: deleteTableWhenAllCellsSelected,
            'Mod-Delete': deleteTableWhenAllCellsSelected
        };
    },
    addProseMirrorPlugins () {
        const isResizable = this.options.resizable && this.editor.isEditable;
        return [
            ...isResizable ? [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["columnResizing"])({
                    handleWidth: this.options.handleWidth,
                    cellMinWidth: this.options.cellMinWidth,
                    defaultCellMinWidth: this.options.cellMinWidth,
                    View: this.options.View,
                    lastColumnResizable: this.options.lastColumnResizable
                })
            ] : [],
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tableEditing"])({
                allowTableNodeSelection: this.options.allowTableNodeSelection
            })
        ];
    },
    extendNodeSchema (extension) {
        const context = {
            name: extension.name,
            options: extension.options,
            storage: extension.storage
        };
        return {
            tableRole: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callOrReturn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getExtensionField"])(extension, 'tableRole', context))
        };
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-table-row/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableRow": (()=>TableRow),
    "default": (()=>TableRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create table rows.
 * @see https://www.tiptap.dev/api/nodes/table-row
 */ const TableRow = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'tableRow',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    content: '(tableCell | tableHeader)*',
    tableRole: 'row',
    parseHTML () {
        return [
            {
                tag: 'tr'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'tr',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-table-header/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableHeader": (()=>TableHeader),
    "default": (()=>TableHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create table headers.
 * @see https://www.tiptap.dev/api/nodes/table-header
 */ const TableHeader = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'tableHeader',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    content: 'block+',
    addAttributes () {
        return {
            colspan: {
                default: 1
            },
            rowspan: {
                default: 1
            },
            colwidth: {
                default: null,
                parseHTML: (element)=>{
                    const colwidth = element.getAttribute('colwidth');
                    const value = colwidth ? colwidth.split(',').map((width)=>parseInt(width, 10)) : null;
                    return value;
                }
            }
        };
    },
    tableRole: 'header_cell',
    isolating: true,
    parseHTML () {
        return [
            {
                tag: 'th'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'th',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@tiptap/extension-table-cell/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableCell": (()=>TableCell),
    "default": (()=>TableCell)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-ssr] (ecmascript)");
;
/**
 * This extension allows you to create table cells.
 * @see https://www.tiptap.dev/api/nodes/table-cell
 */ const TableCell = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Node"].create({
    name: 'tableCell',
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    content: 'block+',
    addAttributes () {
        return {
            colspan: {
                default: 1
            },
            rowspan: {
                default: 1
            },
            colwidth: {
                default: null,
                parseHTML: (element)=>{
                    const colwidth = element.getAttribute('colwidth');
                    const value = colwidth ? colwidth.split(',').map((width)=>parseInt(width, 10)) : null;
                    return value;
                }
            }
        };
    },
    tableRole: 'cell',
    isolating: true,
    parseHTML () {
        return [
            {
                tag: 'td'
            }
        ];
    },
    renderHTML ({ HTMLAttributes }) {
        return [
            'td',
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, HTMLAttributes),
            0
        ];
    }
});
;
 //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=node_modules_%40tiptap_a3e92eb1._.js.map