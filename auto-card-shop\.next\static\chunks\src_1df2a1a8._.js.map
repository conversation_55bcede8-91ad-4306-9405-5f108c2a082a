{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/content-converter.ts"], "sourcesContent": ["import TurndownService from 'turndown'\n\n// HTML转Markdown的配置\nconst turndownService = new TurndownService({\n  headingStyle: 'atx',\n  hr: '---',\n  bulletListMarker: '-',\n  codeBlockStyle: 'fenced',\n  fence: '```',\n  emDelimiter: '*',\n  strongDelimiter: '**',\n  linkStyle: 'inlined',\n  linkReferenceStyle: 'full',\n})\n\n// 自定义规则\nturndownService.addRule('strikethrough', {\n  filter: ['del', 's', 'strike'],\n  replacement: function (content) {\n    return '~~' + content + '~~'\n  }\n})\n\n// 表格规则\nturndownService.addRule('table', {\n  filter: 'table',\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n})\n\nturndownService.addRule('tableRow', {\n  filter: 'tr',\n  replacement: function (content, node) {\n    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')\n    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'\n    \n    if (isHeaderRow) {\n      return '| ' + content + ' |\\n| ' + borderCells + ' |'\n    }\n    return '| ' + content + ' |'\n  }\n})\n\nturndownService.addRule('tableCell', {\n  filter: ['th', 'td'],\n  replacement: function (content) {\n    return content.trim() + ' |'\n  }\n})\n\n/**\n * 将HTML转换为Markdown\n */\nexport function htmlToMarkdown(html: string): string {\n  if (!html) return ''\n  \n  try {\n    return turndownService.turndown(html)\n  } catch (error) {\n    console.error('HTML to Markdown conversion failed:', error)\n    return html\n  }\n}\n\n/**\n * 将Markdown转换为HTML（简单实现）\n */\nexport function markdownToHtml(markdown: string): string {\n  if (!markdown) return ''\n  \n  try {\n    let html = markdown\n    \n    // 标题\n    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')\n    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')\n    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')\n    \n    // 粗体和斜体\n    html = html.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n    html = html.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n    \n    // 代码\n    html = html.replace(/`(.*?)`/g, '<code>$1</code>')\n    \n    // 链接\n    html = html.replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\">$1</a>')\n    \n    // 列表\n    html = html.replace(/^\\s*\\* (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n    \n    html = html.replace(/^\\s*\\d+\\. (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ol>$1</ol>')\n    \n    // 引用\n    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')\n    \n    // 分隔线\n    html = html.replace(/^---$/gm, '<hr>')\n    \n    // 段落\n    html = html.replace(/\\n\\n/g, '</p><p>')\n    html = '<p>' + html + '</p>'\n    \n    // 清理空段落\n    html = html.replace(/<p><\\/p>/g, '')\n    html = html.replace(/<p>(<h[1-6]>)/g, '$1')\n    html = html.replace(/(<\\/h[1-6]>)<\\/p>/g, '$1')\n    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>)/g, '$1')\n    html = html.replace(/(<\\/ul>|<\\/ol>|<\\/blockquote>|<hr>)<\\/p>/g, '$1')\n    \n    return html\n  } catch (error) {\n    console.error('Markdown to HTML conversion failed:', error)\n    return markdown\n  }\n}\n\n/**\n * 检测内容格式\n */\nexport function detectContentFormat(content: string): 'html' | 'markdown' | 'plain' {\n  if (!content) return 'plain'\n\n  // 更严格的HTML标签检测\n  const htmlTagRegex = /<\\/?[a-z][\\s\\S]*>/i\n  const hasHtmlTags = htmlTagRegex.test(content)\n\n  // 检测常见的HTML标签\n  const commonHtmlTags = /<\\/?(?:h[1-6]|p|div|span|strong|em|ul|ol|li|table|tr|td|th|blockquote|a|img|br|hr)\\b[^>]*>/i\n\n  if (hasHtmlTags && commonHtmlTags.test(content)) {\n    return 'html'\n  }\n\n  // 检测Markdown语法\n  const markdownPatterns = [\n    /^#{1,6}\\s+/m,           // 标题\n    /\\*\\*.*?\\*\\*/,           // 粗体\n    /\\*.*?\\*/,               // 斜体\n    /\\[.*?\\]\\(.*?\\)/,        // 链接\n    /^[-*+]\\s+/m,            // 无序列表\n    /^\\d+\\.\\s+/m,            // 有序列表\n    /^>\\s+/m,                // 引用\n    /`.*?`/,                 // 行内代码\n    /^```/m,                 // 代码块\n    /^\\|.*\\|.*$/m,           // 表格\n  ]\n\n  if (markdownPatterns.some(pattern => pattern.test(content))) {\n    return 'markdown'\n  }\n\n  return 'plain'\n}\n\n/**\n * 智能转换内容格式\n */\nexport function convertContent(content: string, targetFormat: 'html' | 'markdown'): string {\n  const currentFormat = detectContentFormat(content)\n  \n  if (currentFormat === targetFormat) {\n    return content\n  }\n  \n  if (currentFormat === 'html' && targetFormat === 'markdown') {\n    return htmlToMarkdown(content)\n  }\n  \n  if (currentFormat === 'markdown' && targetFormat === 'html') {\n    return markdownToHtml(content)\n  }\n  \n  // 纯文本转换\n  if (currentFormat === 'plain') {\n    if (targetFormat === 'html') {\n      return '<p>' + content.replace(/\\n\\n/g, '</p><p>').replace(/\\n/g, '<br>') + '</p>'\n    }\n    return content\n  }\n  \n  return content\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,IAAI,+JAAA,CAAA,UAAe,CAAC;IAC1C,cAAc;IACd,IAAI;IACJ,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,oBAAoB;AACtB;AAEA,QAAQ;AACR,gBAAgB,OAAO,CAAC,iBAAiB;IACvC,QAAQ;QAAC;QAAO;QAAK;KAAS;IAC9B,aAAa,SAAU,OAAO;QAC5B,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,OAAO;AACP,gBAAgB,OAAO,CAAC,SAAS;IAC/B,QAAQ;IACR,aAAa,SAAU,OAAO;QAC5B,OAAO,SAAS,UAAU;IAC5B;AACF;AAEA,gBAAgB,OAAO,CAAC,YAAY;IAClC,QAAQ;IACR,aAAa,SAAU,OAAO,EAAE,IAAI;QAClC,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,IAAM,OAAO,IAAI,CAAC;QACtE,MAAM,cAAc,KAAK,UAAU,EAAE,aAAa;QAElD,IAAI,aAAa;YACf,OAAO,OAAO,UAAU,WAAW,cAAc;QACnD;QACA,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,gBAAgB,OAAO,CAAC,aAAa;IACnC,QAAQ;QAAC;QAAM;KAAK;IACpB,aAAa,SAAU,OAAO;QAC5B,OAAO,QAAQ,IAAI,KAAK;IAC1B;AACF;AAKO,SAAS,eAAe,IAAY;IACzC,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,OAAO,gBAAgB,QAAQ,CAAC;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,eAAe,QAAgB;IAC7C,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,IAAI,OAAO;QAEX,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,iBAAiB;QACrC,OAAO,KAAK,OAAO,CAAC,gBAAgB;QACpC,OAAO,KAAK,OAAO,CAAC,eAAe;QAEnC,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,YAAY;QAEhC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,4BAA4B;QAEhD,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,OAAO,KAAK,OAAO,CAAC,qBAAqB;QACzC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,MAAM;QACN,OAAO,KAAK,OAAO,CAAC,WAAW;QAE/B,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,SAAS;QAC7B,OAAO,QAAQ,OAAO;QAEtB,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,aAAa;QACjC,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,sBAAsB;QAC1C,OAAO,KAAK,OAAO,CAAC,qCAAqC;QACzD,OAAO,KAAK,OAAO,CAAC,6CAA6C;QAEjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAe;IACjD,IAAI,CAAC,SAAS,OAAO;IAErB,eAAe;IACf,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC;IAEtC,cAAc;IACd,MAAM,iBAAiB;IAEvB,IAAI,eAAe,eAAe,IAAI,CAAC,UAAU;QAC/C,OAAO;IACT;IAEA,eAAe;IACf,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,OAAe,EAAE,YAAiC;IAC/E,MAAM,gBAAgB,oBAAoB;IAE1C,IAAI,kBAAkB,cAAc;QAClC,OAAO;IACT;IAEA,IAAI,kBAAkB,UAAU,iBAAiB,YAAY;QAC3D,OAAO,eAAe;IACxB;IAEA,IAAI,kBAAkB,cAAc,iBAAiB,QAAQ;QAC3D,OAAO,eAAe;IACxB;IAEA,QAAQ;IACR,IAAI,kBAAkB,SAAS;QAC7B,IAAI,iBAAiB,QAAQ;YAC3B,OAAO,QAAQ,QAAQ,OAAO,CAAC,SAAS,WAAW,OAAO,CAAC,OAAO,UAAU;QAC9E;QACA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/markdown.tsx"], "sourcesContent": ["import React from 'react'\nimport ReactMarkdown from 'react-markdown'\nimport remarkGfm from 'remark-gfm'\nimport rehypeRaw from 'rehype-raw'\nimport { cn } from '@/lib/utils'\nimport { detectContentFormat } from '@/lib/content-converter'\n\ninterface MarkdownProps {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact'\n}\n\nexport function Markdown({ content, className, variant = 'default' }: MarkdownProps) {\n  const baseClasses = variant === 'compact'\n    ? 'text-sm text-gray-600 line-clamp-3'\n    : 'text-gray-700 leading-relaxed'\n\n  // 检测内容格式\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div\n        className={cn('markdown-content prose prose-sm max-w-none', baseClasses, className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  return (\n    <div className={cn('markdown-content', baseClasses, className)}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeRaw]}\n        components={{\n          // 标题样式\n          h1: ({ children }) => (\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4 mt-6 first:mt-0\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-3 mt-5 first:mt-0\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2 mt-4 first:mt-0\">\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-gray-700 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h6>\n          ),\n          \n          // 段落样式\n          p: ({ children }) => (\n            <p className=\"mb-4 last:mb-0\">\n              {children}\n            </p>\n          ),\n          \n          // 列表样式\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside mb-4 space-y-1\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside mb-4 space-y-1\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-gray-700\">\n              {children}\n            </li>\n          ),\n          \n          // 链接样式\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              className=\"text-blue-600 hover:text-blue-800 underline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // 强调样式\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-gray-900\">\n              {children}\n            </strong>\n          ),\n          em: ({ children }) => (\n            <em className=\"italic\">\n              {children}\n            </em>\n          ),\n          \n          // 代码样式\n          code: ({ children, className }) => {\n            const isInline = !className\n            if (isInline) {\n              return (\n                <code className=\"bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono\">\n                  {children}\n                </code>\n              )\n            }\n            return (\n              <code className=\"block bg-gray-100 text-gray-800 p-3 rounded text-sm font-mono overflow-x-auto\">\n                {children}\n              </code>\n            )\n          },\n          \n          // 引用样式\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-gray-300 pl-4 py-2 mb-4 italic text-gray-600 bg-gray-50\">\n              {children}\n            </blockquote>\n          ),\n          \n          // 分隔线样式\n          hr: () => (\n            <hr className=\"border-gray-300 my-6\" />\n          ),\n          \n          // 表格样式\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-4\">\n              <table className=\"min-w-full border border-gray-300\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-gray-50\">\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-white\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className=\"border-b border-gray-200\">\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className=\"px-4 py-2 text-left font-medium text-gray-900 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"px-4 py-2 text-gray-700 border-r border-gray-200 last:border-r-0\">\n              {children}\n            </td>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n\n// 用于商品卡片的紧凑版本\nexport function MarkdownPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  // 移除Markdown语法，只保留纯文本用于预览\n  const plainText = content\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/>\\s+/g, '') // 移除引用标记\n    .replace(/[-*+]\\s+/g, '') // 移除列表标记\n    .replace(/\\n+/g, ' ') // 将换行替换为空格\n    .trim()\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAiB;IACjF,MAAM,cAAc,YAAY,YAC5B,uCACA;IAEJ,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C,aAAa;YACzE,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,aAAa;kBAClD,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,YAAY;gBACV,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAE,WAAU;kCACV;;;;;;gBAIL,OAAO;gBACP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,WAAU;wBACV,QAAO;wBACP,KAAI;kCAEH;;;;;;gBAIL,OAAO;gBACP,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAU;kCACf;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;oBAC5B,MAAM,WAAW,CAAC;oBAClB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBACA,qBACE,6LAAC;wBAAK,WAAU;kCACb;;;;;;gBAGP;gBAEA,OAAO;gBACP,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAU;kCACnB;;;;;;gBAIL,QAAQ;gBACR,IAAI,kBACF,6LAAC;wBAAG,WAAU;;;;;;gBAGhB,OAAO;gBACP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;YAGP;sBAEC;;;;;;;;;;;AAIT;KA5KgB;AA+KT,SAAS,gBAAgB,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IACnG,0BAA0B;IAC1B,MAAM,YAAY,QACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;KAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,QAAQ,KAAK,WAAW;KAChC,IAAI;IAEP,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MAtBgB", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/rich-text-editor.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useEdit<PERSON>, EditorContent } from '@tiptap/react'\nimport StarterKit from '@tiptap/starter-kit'\nimport Link from '@tiptap/extension-link'\nimport Image from '@tiptap/extension-image'\nimport Table from '@tiptap/extension-table'\nimport TableRow from '@tiptap/extension-table-row'\nimport TableHeader from '@tiptap/extension-table-header'\nimport TableCell from '@tiptap/extension-table-cell'\nimport { Button } from './button'\nimport { cn } from '@/lib/utils'\nimport {\n  Bold,\n  Italic,\n  List,\n  ListOrdered,\n  Quote,\n  Undo,\n  Redo,\n  Link as LinkIcon,\n  Table as TableIcon,\n  Code,\n  Heading1,\n  Heading2,\n  Heading3,\n  Image as ImageIcon,\n  Minus,\n} from 'lucide-react'\n\ninterface RichTextEditorProps {\n  content: string\n  onChange: (content: string) => void\n  placeholder?: string\n  className?: string\n}\n\nexport function RichTextEditor({\n  content,\n  onChange,\n  placeholder = '请输入内容...',\n  className,\n}: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Link.configure({\n        openOnClick: false,\n        HTMLAttributes: {\n          class: 'text-blue-600 hover:text-blue-800 underline',\n        },\n      }),\n      Image.configure({\n        HTMLAttributes: {\n          class: 'max-w-full h-auto rounded-lg',\n        },\n      }),\n      Table.configure({\n        resizable: true,\n      }),\n      TableRow,\n      TableHeader,\n      TableCell,\n    ],\n    content,\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML())\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-sm max-w-none focus:outline-none min-h-[200px] p-4',\n      },\n    },\n  })\n\n  if (!editor) {\n    return null\n  }\n\n  const addLink = () => {\n    const url = window.prompt('请输入链接地址:')\n    if (url) {\n      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()\n    }\n  }\n\n  const addTable = () => {\n    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()\n  }\n\n  const addImage = () => {\n    const url = window.prompt('请输入图片地址:')\n    if (url) {\n      editor.chain().focus().setImage({ src: url }).run()\n    }\n  }\n\n  return (\n    <div className={cn('border border-gray-300 rounded-md', className)}>\n      {/* 工具栏 */}\n      <div className=\"border-b border-gray-200 p-2 flex flex-wrap gap-1\">\n        {/* 标题 */}\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className={editor.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}\n        >\n          <Heading1 className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}\n        >\n          <Heading2 className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className={editor.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}\n        >\n          <Heading3 className=\"w-4 h-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n\n        {/* 文本格式 */}\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={editor.isActive('bold') ? 'bg-gray-200' : ''}\n        >\n          <Bold className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={editor.isActive('italic') ? 'bg-gray-200' : ''}\n        >\n          <Italic className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleCode().run()}\n          className={editor.isActive('code') ? 'bg-gray-200' : ''}\n        >\n          <Code className=\"w-4 h-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n\n        {/* 列表 */}\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}\n        >\n          <List className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}\n        >\n          <ListOrdered className=\"w-4 h-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n\n        {/* 引用和其他 */}\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}\n        >\n          <Quote className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={addLink}\n        >\n          <LinkIcon className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={addTable}\n        >\n          <TableIcon className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={addImage}\n        >\n          <ImageIcon className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().setHorizontalRule().run()}\n        >\n          <Minus className=\"w-4 h-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n\n        {/* 撤销重做 */}\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().undo()}\n        >\n          <Undo className=\"w-4 h-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().redo()}\n        >\n          <Redo className=\"w-4 h-4\" />\n        </Button>\n      </div>\n\n      {/* 编辑器内容 */}\n      <EditorContent\n        editor={editor}\n        className=\"min-h-[200px] max-h-[400px] overflow-y-auto\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;AAsCO,SAAS,eAAe,EAC7B,OAAO,EACP,QAAQ,EACR,cAAc,UAAU,EACxB,SAAS,EACW;;IACpB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,iKAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBACb,aAAa;gBACb,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;gBACd,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;gBACd,WAAW;YACb;YACA,yKAAA,CAAA,UAAQ;YACR,4KAAA,CAAA,UAAW;YACX,0KAAA,CAAA,UAAS;SACV;QACD;QACA,QAAQ;gDAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,SAAS,OAAO,OAAO;YACzB;;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,MAAM,OAAO,MAAM,CAAC;QAC1B,IAAI,KAAK;YACP,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC,QAAQ,OAAO,CAAC;gBAAE,MAAM;YAAI,GAAG,GAAG;QAC3E;IACF;IAEA,MAAM,WAAW;QACf,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC;YAAE,MAAM;YAAG,MAAM;YAAG,eAAe;QAAK,GAAG,GAAG;IACnF;IAEA,MAAM,WAAW;QACf,MAAM,MAAM,OAAO,MAAM,CAAC;QAC1B,IAAI,KAAK;YACP,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;gBAAE,KAAK;YAAI,GAAG,GAAG;QACnD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;;0BAEtD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,gBAAgB;kCAEtE,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,gBAAgB;kCAEtE,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,gBAAgB;kCAEtE,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,OAAO,QAAQ,CAAC,UAAU,gBAAgB;kCAErD,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,OAAO,QAAQ,CAAC,YAAY,gBAAgB;kCAEvD,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,OAAO,QAAQ,CAAC,UAAU,gBAAgB;kCAErD,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,OAAO,QAAQ,CAAC,gBAAgB,gBAAgB;kCAE3D,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,OAAO,QAAQ,CAAC,iBAAiB,gBAAgB;kCAE5D,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,OAAO,QAAQ,CAAC,gBAAgB,gBAAgB;kCAE3D,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;kCAET,cAAA,6LAAC,qMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;kCAET,cAAA,6LAAC,uMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;kCAET,cAAA,6LAAC,uMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;kCAE7D,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;kCAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;kCAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKpB,6LAAC,qKAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,WAAU;;;;;;;;;;;;AAIlB;GA7NgB;;QAMC,qKAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/products/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Markdown } from '@/components/ui/markdown'\nimport { ContentPreview } from '@/components/ui/content-renderer'\nimport { RichTextEditor } from '@/components/ui/rich-text-editor'\nimport { formatPrice, formatDate } from '@/lib/utils'\nimport { htmlToMarkdown, markdownToHtml, detectContentFormat, convertContent } from '@/lib/content-converter'\nimport { Plus, Edit, Trash2, Eye, FileText, Type, Code } from 'lucide-react'\n\ninterface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image: string\n  status: string\n  stockCount: number\n  createdAt: string\n  category: {\n    name: string\n  }\n  _count: {\n    cards: number\n  }\n}\n\ninterface Category {\n  id: string\n  name: string\n}\n\nexport default function ProductsManagement() {\n  const [products, setProducts] = useState<Product[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  // 表单状态\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    categoryId: '',\n    image: '',\n    status: 'ACTIVE'\n  })\n  const [showMarkdownPreview, setShowMarkdownPreview] = useState(false)\n  const [editorMode, setEditorMode] = useState<'rich' | 'markdown'>('rich')\n\n  useEffect(() => {\n    fetchProducts()\n    fetchCategories()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products?status=all')\n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('获取商品失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchCategories = async () => {\n    try {\n      const response = await fetch('/api/categories')\n      const data = await response.json()\n      setCategories(data)\n    } catch (error) {\n      console.error('获取分类失败:', error)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    try {\n      const url = editingProduct ? `/api/products/${editingProduct.id}` : '/api/products'\n      const method = editingProduct ? 'PUT' : 'POST'\n      \n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...formData,\n          price: parseFloat(formData.price)\n        }),\n      })\n\n      if (response.ok) {\n        await fetchProducts()\n        resetForm()\n        alert(editingProduct ? '商品更新成功' : '商品创建成功')\n      } else {\n        const error = await response.json()\n        alert(error.error || '操作失败')\n      }\n    } catch (error) {\n      alert('操作失败，请重试')\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setFormData({\n      name: product.name,\n      description: product.description || '',\n      price: product.price.toString(),\n      categoryId: product.category.id || '',\n      image: product.image || '',\n      status: product.status\n    })\n    setShowAddForm(true)\n  }\n\n  const handleDelete = async (productId: string) => {\n    if (!confirm('确定要删除这个商品吗？')) return\n\n    try {\n      const response = await fetch(`/api/products/${productId}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        await fetchProducts()\n        alert('商品删除成功')\n      } else {\n        alert('删除失败')\n      }\n    } catch (error) {\n      alert('删除失败，请重试')\n    }\n  }\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      categoryId: '',\n      image: '',\n      status: 'ACTIVE'\n    })\n    setEditingProduct(null)\n    setShowAddForm(false)\n    setShowMarkdownPreview(false)\n    setEditorMode('rich')\n  }\n\n  // 切换编辑器模式\n  const switchEditorMode = (newMode: 'rich' | 'markdown') => {\n    if (newMode === editorMode) return\n\n    let convertedContent = formData.description\n\n    if (newMode === 'markdown' && editorMode === 'rich') {\n      // 从富文本转换到Markdown\n      convertedContent = htmlToMarkdown(formData.description)\n    } else if (newMode === 'rich' && editorMode === 'markdown') {\n      // 从Markdown转换到富文本\n      convertedContent = markdownToHtml(formData.description)\n    }\n\n    setFormData({ ...formData, description: convertedContent })\n    setEditorMode(newMode)\n    setShowMarkdownPreview(false)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return 'bg-green-100 text-green-800'\n      case 'INACTIVE':\n        return 'bg-gray-100 text-gray-800'\n      case 'OUT_OF_STOCK':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return '上架'\n      case 'INACTIVE':\n        return '下架'\n      case 'OUT_OF_STOCK':\n        return '缺货'\n      default:\n        return status\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">商品管理</h1>\n          <p className=\"text-gray-600\">管理网站上的所有商品</p>\n        </div>\n        <Button onClick={() => setShowAddForm(true)}>\n          <Plus className=\"w-4 h-4 mr-2\" />\n          添加商品\n        </Button>\n      </div>\n\n      {/* 添加/编辑表单 */}\n      {showAddForm && (\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-lg font-semibold mb-4\">\n            {editingProduct ? '编辑商品' : '添加商品'}\n          </h2>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  商品名称 *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  价格 *\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  required\n                  value={formData.price}\n                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  分类 *\n                </label>\n                <select\n                  required\n                  value={formData.categoryId}\n                  onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">选择分类</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  状态\n                </label>\n                <select\n                  value={formData.status}\n                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"ACTIVE\">上架</option>\n                  <option value=\"INACTIVE\">下架</option>\n                  <option value=\"OUT_OF_STOCK\">缺货</option>\n                </select>\n              </div>\n            </div>\n            \n            <div>\n              <div className=\"flex items-center justify-between mb-3\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  商品描述\n                </label>\n                <div className=\"flex items-center space-x-2\">\n                  {/* 编辑器模式切换 */}\n                  <div className=\"flex items-center bg-gray-100 rounded-md p-1\">\n                    <Button\n                      type=\"button\"\n                      variant={editorMode === 'rich' ? 'default' : 'ghost'}\n                      size=\"sm\"\n                      onClick={() => switchEditorMode('rich')}\n                      className=\"h-7 px-2 text-xs\"\n                    >\n                      <Type className=\"w-3 h-3 mr-1\" />\n                      富文本\n                    </Button>\n                    <Button\n                      type=\"button\"\n                      variant={editorMode === 'markdown' ? 'default' : 'ghost'}\n                      size=\"sm\"\n                      onClick={() => switchEditorMode('markdown')}\n                      className=\"h-7 px-2 text-xs\"\n                    >\n                      <Code className=\"w-3 h-3 mr-1\" />\n                      Markdown\n                    </Button>\n                  </div>\n\n                  {/* 预览按钮（仅在Markdown模式下显示） */}\n                  {editorMode === 'markdown' && (\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => setShowMarkdownPreview(!showMarkdownPreview)}\n                    >\n                      <FileText className=\"w-4 h-4 mr-1\" />\n                      {showMarkdownPreview ? '编辑' : '预览'}\n                    </Button>\n                  )}\n                </div>\n              </div>\n\n              {editorMode === 'rich' ? (\n                <RichTextEditor\n                  content={formData.description}\n                  onChange={(content) => setFormData({ ...formData, description: content })}\n                  placeholder=\"请输入商品描述...\"\n                />\n              ) : showMarkdownPreview ? (\n                <div className=\"w-full min-h-[200px] px-3 py-2 border border-gray-300 rounded-md bg-gray-50\">\n                  {formData.description ? (\n                    <Markdown content={formData.description} />\n                  ) : (\n                    <p className=\"text-gray-500 italic\">暂无内容</p>\n                  )}\n                </div>\n              ) : (\n                <textarea\n                  rows={8}\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm\"\n                  placeholder=\"支持 Markdown 格式，例如：&#10;# 标题&#10;**粗体文字**&#10;- 列表项&#10;[链接](https://example.com)\"\n                />\n              )}\n\n              <div className=\"mt-2 text-xs text-gray-500\">\n                {editorMode === 'rich'\n                  ? '使用富文本编辑器创建格式化内容，支持标题、列表、链接、表格等'\n                  : '支持 Markdown 语法：标题 (#)、粗体 (**)、斜体 (*)、列表 (-)、链接 ([文字](URL)) 等'\n                }\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                商品图片 URL\n              </label>\n              <input\n                type=\"url\"\n                value={formData.image}\n                onChange={(e) => setFormData({ ...formData, image: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"https://example.com/image.jpg\"\n              />\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              <Button type=\"submit\">\n                {editingProduct ? '更新商品' : '创建商品'}\n              </Button>\n              <Button type=\"button\" variant=\"outline\" onClick={resetForm}>\n                取消\n              </Button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* 商品列表 */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  商品信息\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  分类\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  价格\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  库存\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  状态\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  创建时间\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {products.map((product) => (\n                <tr key={product.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      {product.image && (\n                        <img\n                          src={product.image}\n                          alt={product.name}\n                          className=\"w-10 h-10 rounded-lg object-cover mr-3\"\n                        />\n                      )}\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {product.name}\n                        </div>\n                        {product.description && (\n                          <div className=\"text-sm text-gray-500 max-w-xs\">\n                            <MarkdownPreview content={product.description} maxLength={80} />\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category.name}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600\">\n                    {formatPrice(product.price)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product._count.cards} 张\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>\n                      {getStatusText(product.status)}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDate(product.createdAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleEdit(product)}\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleDelete(product.id)}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {products.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-500\">暂无商品</div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,OAAO;QACP,QAAQ;IACV;IACA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;YACA;QACF;uCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,MAAM,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,EAAE,GAAG;YACpE,MAAM,SAAS,iBAAiB,QAAQ;YAExC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,OAAO,WAAW,SAAS,KAAK;gBAClC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN;gBACA,MAAM,iBAAiB,WAAW;YACpC,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,YAAY;YACV,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW,IAAI;YACpC,OAAO,QAAQ,KAAK,CAAC,QAAQ;YAC7B,YAAY,QAAQ,QAAQ,CAAC,EAAE,IAAI;YACnC,OAAO,QAAQ,KAAK,IAAI;YACxB,QAAQ,QAAQ,MAAM;QACxB;QACA,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,aAAa;YACb,OAAO;YACP,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,kBAAkB;QAClB,eAAe;QACf,uBAAuB;QACvB,cAAc;IAChB;IAEA,UAAU;IACV,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY,YAAY;QAE5B,IAAI,mBAAmB,SAAS,WAAW;QAE3C,IAAI,YAAY,cAAc,eAAe,QAAQ;YACnD,kBAAkB;YAClB,mBAAmB,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW;QACxD,OAAO,IAAI,YAAY,UAAU,eAAe,YAAY;YAC1D,kBAAkB;YAClB,mBAAmB,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW;QACxD;QAEA,YAAY;YAAE,GAAG,QAAQ;YAAE,aAAa;QAAiB;QACzD,cAAc;QACd,uBAAuB;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,eAAe;;0CACpC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,iBAAiB,SAAS;;;;;;kCAG7B,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAClE,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,QAAQ;gDACR,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACvE,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAO9B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAKnC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,eAAe,SAAS,YAAY;gEAC7C,MAAK;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,eAAe,aAAa,YAAY;gEACjD,MAAK;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;oDAMpC,eAAe,4BACd,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,uBAAuB,CAAC;;0EAEvC,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,sBAAsB,OAAO;;;;;;;;;;;;;;;;;;;oCAMrC,eAAe,uBACd,6LAAC,qJAAA,CAAA,iBAAc;wCACb,SAAS,SAAS,WAAW;wCAC7B,UAAU,CAAC,UAAY,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa;4CAAQ;wCACvE,aAAY;;;;;+CAEZ,oCACF,6LAAC;wCAAI,WAAU;kDACZ,SAAS,WAAW,iBACnB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,SAAS,SAAS,WAAW;;;;;iEAEvC,6LAAC;4CAAE,WAAU;sDAAuB;;;;;;;;;;6DAIxC,6LAAC;wCACC,MAAM;wCACN,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACxE,WAAU;wCACV,aAAY;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;kDACZ,eAAe,SACZ,mCACA;;;;;;;;;;;;0CAKR,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAClE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;kDACV,iBAAiB,SAAS;;;;;;kDAE7B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;0BASpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,KAAK,kBACZ,6LAAC;4DACC,KAAK,QAAQ,KAAK;4DAClB,KAAK,QAAQ,IAAI;4DACjB,WAAU;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;gEAEd,QAAQ,WAAW,kBAClB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAgB,SAAS,QAAQ,WAAW;wEAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpE,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,IAAI;;;;;;0DAExB,6LAAC;gDAAG,WAAU;0DACX,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;0DAE5B,6LAAC;gDAAG,WAAU;;oDACX,QAAQ,MAAM,CAAC,KAAK;oDAAC;;;;;;;0DAExB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;8DAC5F,cAAc,QAAQ,MAAM;;;;;;;;;;;0DAGjC,6LAAC;gDAAG,WAAU;0DACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,SAAS;;;;;;0DAE/B,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,WAAW;kEAE1B,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa,QAAQ,EAAE;kEAEtC,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;uCApDf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YA8D5B,SAAS,MAAM,KAAK,mBACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAKzC;GA/cwB;KAAA", "debugId": null}}]}